2025-08-02 10:34:48,958 - [Main] - [INFO] - --- Phase 1: Preparing Environment ---
2025-08-02 10:34:48,958 - [Main] - [INFO] - Preparation tasks will use GPU: 7
2025-08-02 10:34:48,958 - [Main] - [INFO] - Using pretrained DiT model: /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/models/DiT-XL-2-256x256.pt
2025-08-02 10:34:48,959 - [Main] - [INFO] - Using local VAE model: /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/models/local_vae_model
2025-08-02 10:34:48,959 - [Main] - [INFO] - Reference images already exist (24 images), skipping generation.
2025-08-02 10:34:48,959 - [Main] - [INFO] - Running in Single-GPU mode.
2025-08-02 10:34:48,959 - [qua2sedimo-GPU7] - [INFO] - --- Starting benchmark for qua2sedimo on GPU 7 ---
2025-08-02 10:34:48,959 - [qua2sedimo-GPU7] - [INFO] - Qua2SeDiMo multiquantizer checkpoint already exists at /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/github-repos/Qua2SeDiMo/multiquantizers/dit_mq.pt, skipping generation step.
2025-08-02 10:34:48,960 - [qua2sedimo-GPU7] - [INFO] - Sampling for FID with Qua2SeDiMo...
2025-08-02 10:34:48,960 - [qua2sedimo-GPU7] - [INFO] - Executing in '/home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/github-repos/Qua2SeDiMo':
$ python -u /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/results/qua2sedimo_w4a8/qua2sedimo_inference.py --outdir /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/results/qua2sedimo_w4a8/fid_samples --weight_bit 4 --multi_chkpt /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/github-repos/Qua2SeDiMo/multiquantizers/dit_mq.pt --seed 1234 --n_imgs 1000 --res 256 --num_inference_steps 50 --dit_ckpt /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/models/DiT-XL-2-256x256.pt --vae_path /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/models/local_vae_model

2025-08-02 10:34:54,608 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Global seed set to 1234
2025-08-02 10:34:54,610 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] 2025-08-02 10:34:54,610 - INFO - Attempting to load local DiT model and VAE...
2025-08-02 10:35:07,853 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] An error occurred while trying to fetch /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/models/local_vae_model: Error no file named diffusion_pytorch_model.safetensors found in directory /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/models/local_vae_model.
2025-08-02 10:35:07,853 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Defaulting to unsafe serialization. Pass `allow_pickle=False` to raise an error instead.
2025-08-02 10:35:08,017 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] 2025-08-02 10:35:08,017 - INFO - Successfully loaded local models
2025-08-02 10:35:08,017 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] 2025-08-02 10:35:08,017 - INFO - Setting up quantization for local DiT model...
2025-08-02 10:35:08,088 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] 2025-08-02 10:35:08,088 - INFO - Loading multiquantizer checkpoint from /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/github-repos/Qua2SeDiMo/multiquantizers/dit_mq.pt
2025-08-02 10:35:15,021 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] 2025-08-02 10:35:15,021 - INFO - Generating 1000 random images
2025-08-02 10:35:15,022 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] 2025-08-02 10:35:15,022 - INFO - Generating random images with local DiT model...
2025-08-02 10:35:15,036 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Traceback (most recent call last):
2025-08-02 10:35:15,036 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] File "/home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/results/qua2sedimo_w4a8/qua2sedimo_inference.py", line 233, in <module>
2025-08-02 10:35:15,036 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] main()
2025-08-02 10:35:15,036 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] File "/home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/results/qua2sedimo_w4a8/qua2sedimo_inference.py", line 206, in main
2025-08-02 10:35:15,036 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] samples = diffusion.p_sample_loop(
2025-08-02 10:35:15,036 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] File "/home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/github-repos/PTQ4DiT/diffusion/gaussian_diffusion.py", line 450, in p_sample_loop
2025-08-02 10:35:15,037 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] for sample in self.p_sample_loop_progressive(
2025-08-02 10:35:15,037 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] File "/home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/github-repos/PTQ4DiT/diffusion/gaussian_diffusion.py", line 501, in p_sample_loop_progressive
2025-08-02 10:35:15,037 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] out = self.p_sample(
2025-08-02 10:35:15,037 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] File "/home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/github-repos/PTQ4DiT/diffusion/gaussian_diffusion.py", line 402, in p_sample
2025-08-02 10:35:15,037 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] out = self.p_mean_variance(
2025-08-02 10:35:15,037 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] File "/home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/github-repos/PTQ4DiT/diffusion/respace.py", line 92, in p_mean_variance
2025-08-02 10:35:15,037 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] return super().p_mean_variance(self._wrap_model(model), *args, **kwargs)
2025-08-02 10:35:15,037 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] File "/home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/github-repos/PTQ4DiT/diffusion/gaussian_diffusion.py", line 279, in p_mean_variance
2025-08-02 10:35:15,037 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] model_output = model(x, t, **model_kwargs)
2025-08-02 10:35:15,038 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] File "/home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/github-repos/PTQ4DiT/diffusion/respace.py", line 130, in __call__
2025-08-02 10:35:15,038 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] return self.model(x, new_ts, **kwargs)
2025-08-02 10:35:15,038 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] File "/home/<USER>/.conda/envs/dquant-rag2/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1751, in _wrapped_call_impl
2025-08-02 10:35:15,038 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] return self._call_impl(*args, **kwargs)
2025-08-02 10:35:15,038 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] File "/home/<USER>/.conda/envs/dquant-rag2/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1762, in _call_impl
2025-08-02 10:35:15,038 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] return forward_call(*args, **kwargs)
2025-08-02 10:35:15,038 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] TypeError: QuantModelMultiQ.forward() got an unexpected keyword argument 'y'
2025-08-02 10:35:16,740 - [qua2sedimo-GPU7] - [ERROR] - Benchmark for qua2sedimo failed.
Traceback (most recent call last):
  File "/home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/run_benchmark.py", line 1194, in main
    all_results[task['method']] = task['func'](env_vars)
  File "/home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/run_benchmark.py", line 584, in benchmark_qua2sedimo
    run_script([str(custom_infer_script)] + [
  File "/home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/run_benchmark.py", line 127, in run_script
    raise subprocess.CalledProcessError(return_code, final_command, "\n".join(output_lines))
subprocess.CalledProcessError: Command '['python', '-u', '/home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/results/qua2sedimo_w4a8/qua2sedimo_inference.py', '--outdir', '/home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/results/qua2sedimo_w4a8/fid_samples', '--weight_bit', '4', '--multi_chkpt', '/home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/github-repos/Qua2SeDiMo/multiquantizers/dit_mq.pt', '--seed', '1234', '--n_imgs', '1000', '--res', '256', '--num_inference_steps', '50', '--dit_ckpt', '/home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/models/DiT-XL-2-256x256.pt', '--vae_path', '/home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/models/local_vae_model']' returned non-zero exit status 1.
2025-08-02 10:35:16,740 - [qua2sedimo-GPU7] - [WARNING] - No benchmark results were successfully generated.