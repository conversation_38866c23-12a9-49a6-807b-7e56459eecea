2025-08-02 10:38:49,749 - [Main] - [INFO] - --- Phase 1: Preparing Environment ---
2025-08-02 10:38:49,749 - [Main] - [INFO] - Preparation tasks will use GPU: 7
2025-08-02 10:38:49,749 - [Main] - [INFO] - Using pretrained DiT model: /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/models/DiT-XL-2-256x256.pt
2025-08-02 10:38:49,749 - [Main] - [INFO] - Using local VAE model: /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/models/local_vae_model
2025-08-02 10:38:49,749 - [Main] - [INFO] - Reference images already exist (24 images), skipping generation.
2025-08-02 10:38:49,749 - [Main] - [INFO] - Running in Single-GPU mode.
2025-08-02 10:38:49,750 - [qua2sedimo-GPU7] - [INFO] - --- Starting benchmark for qua2sedimo on GPU 7 ---
2025-08-02 10:38:49,750 - [qua2sedimo-GPU7] - [INFO] - Qua2SeDiMo multiquantizer checkpoint already exists at /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/github-repos/Qua2SeDiMo/multiquantizers/dit_mq.pt, skipping generation step.
2025-08-02 10:38:49,750 - [qua2sedimo-GPU7] - [INFO] - Sampling for FID with Qua2SeDiMo...
2025-08-02 10:38:49,750 - [qua2sedimo-GPU7] - [INFO] - Executing in '/home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/github-repos/Qua2SeDiMo':
$ python -u /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/results/qua2sedimo_w4a8/qua2sedimo_inference.py --outdir /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/results/qua2sedimo_w4a8/fid_samples --weight_bit 4 --multi_chkpt /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/github-repos/Qua2SeDiMo/multiquantizers/dit_mq.pt --seed 1234 --n_imgs 1000 --res 256 --num_inference_steps 50 --dit_ckpt /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/models/DiT-XL-2-256x256.pt --vae_path /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/models/local_vae_model

2025-08-02 10:38:55,269 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Global seed set to 1234
2025-08-02 10:38:55,270 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] 2025-08-02 10:38:55,270 - INFO - Attempting to load local DiT model and VAE...
2025-08-02 10:39:08,987 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] An error occurred while trying to fetch /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/models/local_vae_model: Error no file named diffusion_pytorch_model.safetensors found in directory /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/models/local_vae_model.
2025-08-02 10:39:08,988 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Defaulting to unsafe serialization. Pass `allow_pickle=False` to raise an error instead.
2025-08-02 10:39:09,148 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] 2025-08-02 10:39:09,148 - INFO - Successfully loaded local models
2025-08-02 10:39:09,148 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] 2025-08-02 10:39:09,148 - INFO - Setting up quantization for local DiT model...
2025-08-02 10:39:09,217 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] 2025-08-02 10:39:09,217 - INFO - Loading multiquantizer checkpoint from /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/github-repos/Qua2SeDiMo/multiquantizers/dit_mq.pt
2025-08-02 10:39:16,500 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] 2025-08-02 10:39:16,500 - INFO - Generating 1000 random images
2025-08-02 10:39:16,501 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] 2025-08-02 10:39:16,501 - INFO - Generating random images with local DiT model...
2025-08-02 10:39:16,503 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Traceback (most recent call last):
2025-08-02 10:39:16,503 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] File "/home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/results/qua2sedimo_w4a8/qua2sedimo_inference.py", line 246, in <module>
2025-08-02 10:39:16,503 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] main()
2025-08-02 10:39:16,503 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] File "/home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/results/qua2sedimo_w4a8/qua2sedimo_inference.py", line 219, in main
2025-08-02 10:39:16,504 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] samples = diffusion.p_sample_loop(
2025-08-02 10:39:16,504 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] File "/home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/github-repos/PTQ4DiT/diffusion/gaussian_diffusion.py", line 450, in p_sample_loop
2025-08-02 10:39:16,504 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] for sample in self.p_sample_loop_progressive(
2025-08-02 10:39:16,504 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] File "/home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/github-repos/PTQ4DiT/diffusion/gaussian_diffusion.py", line 484, in p_sample_loop_progressive
2025-08-02 10:39:16,504 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] device = next(model.parameters()).device
2025-08-02 10:39:16,504 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] AttributeError: 'DiTQuantWrapper' object has no attribute 'parameters'
2025-08-02 10:39:18,312 - [qua2sedimo-GPU7] - [ERROR] - Benchmark for qua2sedimo failed.
Traceback (most recent call last):
  File "/home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/run_benchmark.py", line 1207, in main
    all_results[task['method']] = task['func'](env_vars)
  File "/home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/run_benchmark.py", line 584, in benchmark_qua2sedimo
    run_script([str(custom_infer_script)] + [
  File "/home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/run_benchmark.py", line 127, in run_script
    raise subprocess.CalledProcessError(return_code, final_command, "\n".join(output_lines))
subprocess.CalledProcessError: Command '['python', '-u', '/home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/results/qua2sedimo_w4a8/qua2sedimo_inference.py', '--outdir', '/home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/results/qua2sedimo_w4a8/fid_samples', '--weight_bit', '4', '--multi_chkpt', '/home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/github-repos/Qua2SeDiMo/multiquantizers/dit_mq.pt', '--seed', '1234', '--n_imgs', '1000', '--res', '256', '--num_inference_steps', '50', '--dit_ckpt', '/home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/models/DiT-XL-2-256x256.pt', '--vae_path', '/home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/models/local_vae_model']' returned non-zero exit status 1.
2025-08-02 10:39:18,312 - [qua2sedimo-GPU7] - [WARNING] - No benchmark results were successfully generated.