2025-08-02 21:16:22,666 - [Main] - [INFO] - --- Phase 1: Preparing Environment ---
2025-08-02 21:16:22,666 - [Main] - [INFO] - Preparation tasks will use GPU: 7
2025-08-02 21:16:22,666 - [Main] - [INFO] - Using pretrained DiT model: /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/models/DiT-XL-2-256x256.pt
2025-08-02 21:16:22,666 - [Main] - [INFO] - Using local VAE model: /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/models/local_vae_model
2025-08-02 21:16:22,666 - [Main] - [INFO] - Reference images already exist (24 images), skipping generation.
2025-08-02 21:16:22,666 - [Main] - [INFO] - Running in Single-GPU mode.
2025-08-02 21:16:22,667 - [qua2sedimo-GPU7] - [INFO] - --- Starting benchmark for qua2sedimo on GPU 7 ---
2025-08-02 21:16:22,667 - [qua2sedimo-GPU7] - [INFO] - Qua2SeDiMo multiquantizer checkpoint already exists at /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/github-repos/Qua2SeDiMo/multiquantizers/dit_mq.pt, skipping generation step.
2025-08-02 21:16:22,667 - [qua2sedimo-GPU7] - [INFO] - Sampling for FID with Qua2SeDiMo...
2025-08-02 21:16:22,667 - [qua2sedimo-GPU7] - [INFO] - Executing in '/home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/github-repos/Qua2SeDiMo':
$ python -u /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/results/qua2sedimo_w4a8/qua2sedimo_inference.py --outdir /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/results/qua2sedimo_w4a8/fid_samples --weight_bit 4 --multi_chkpt /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/github-repos/Qua2SeDiMo/multiquantizers/dit_mq.pt --seed 1234 --n_imgs 1000 --res 256 --num_inference_steps 50 --dit_ckpt /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/models/DiT-XL-2-256x256.pt --vae_path /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/models/local_vae_model

2025-08-02 21:16:28,381 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Global seed set to 1234
2025-08-02 21:16:28,382 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] 2025-08-02 21:16:28,382 - INFO - Attempting to load local DiT model and VAE...
2025-08-02 21:16:41,287 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] An error occurred while trying to fetch /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/models/local_vae_model: Error no file named diffusion_pytorch_model.safetensors found in directory /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/models/local_vae_model.
2025-08-02 21:16:41,287 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Defaulting to unsafe serialization. Pass `allow_pickle=False` to raise an error instead.
2025-08-02 21:16:41,439 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] 2025-08-02 21:16:41,439 - INFO - Successfully loaded local models
2025-08-02 21:16:41,439 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] 2025-08-02 21:16:41,439 - INFO - Setting up quantization for local DiT model...
2025-08-02 21:16:41,511 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] 2025-08-02 21:16:41,511 - INFO - Loading multiquantizer checkpoint from /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/github-repos/Qua2SeDiMo/multiquantizers/dit_mq.pt
2025-08-02 21:16:48,773 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] 2025-08-02 21:16:48,773 - INFO - Successfully loaded multiquantizer checkpoint
2025-08-02 21:16:48,774 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] 2025-08-02 21:16:48,773 - INFO - Setting quantization state...
2025-08-02 21:16:48,776 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] 2025-08-02 21:16:48,776 - INFO - Quantization state set successfully
2025-08-02 21:16:48,776 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] 2025-08-02 21:16:48,776 - INFO - Generating 1000 random images
2025-08-02 21:16:48,776 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] 2025-08-02 21:16:48,776 - INFO - Generating random images with local DiT model...
2025-08-02 21:16:48,792 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Unable to determine original precision. Assuming FP32.
2025-08-02 21:16:48,901 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Unable to determine original precision. Assuming FP32.
2025-08-02 21:16:48,911 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Unable to determine original precision. Assuming FP32.
2025-08-02 21:16:48,937 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Unable to determine original precision. Assuming FP32.
2025-08-02 21:16:49,005 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Unable to determine original precision. Assuming FP32.
2025-08-02 21:16:49,017 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Unable to determine original precision. Assuming FP32.
2025-08-02 21:16:49,026 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Unable to determine original precision. Assuming FP32.
2025-08-02 21:16:49,039 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Unable to determine original precision. Assuming FP32.
2025-08-02 21:16:49,044 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Unable to determine original precision. Assuming FP32.
2025-08-02 21:16:49,091 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Unable to determine original precision. Assuming FP32.
2025-08-02 21:16:49,097 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Unable to determine original precision. Assuming FP32.
2025-08-02 21:16:49,101 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Unable to determine original precision. Assuming FP32.
2025-08-02 21:16:49,110 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Unable to determine original precision. Assuming FP32.
2025-08-02 21:16:49,115 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Unable to determine original precision. Assuming FP32.
2025-08-02 21:16:49,161 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Unable to determine original precision. Assuming FP32.
2025-08-02 21:16:49,167 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Unable to determine original precision. Assuming FP32.
2025-08-02 21:16:49,171 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Unable to determine original precision. Assuming FP32.
2025-08-02 21:16:49,180 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Unable to determine original precision. Assuming FP32.
2025-08-02 21:16:49,185 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Unable to determine original precision. Assuming FP32.
2025-08-02 21:16:49,202 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Unable to determine original precision. Assuming FP32.
2025-08-02 21:16:49,211 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Unable to determine original precision. Assuming FP32.
2025-08-02 21:16:49,214 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Unable to determine original precision. Assuming FP32.
2025-08-02 21:16:49,224 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Unable to determine original precision. Assuming FP32.
2025-08-02 21:16:49,229 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Unable to determine original precision. Assuming FP32.
2025-08-02 21:16:49,246 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Unable to determine original precision. Assuming FP32.
2025-08-02 21:16:49,255 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Unable to determine original precision. Assuming FP32.
2025-08-02 21:16:49,258 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Unable to determine original precision. Assuming FP32.
2025-08-02 21:16:49,267 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Unable to determine original precision. Assuming FP32.
2025-08-02 21:16:49,272 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Unable to determine original precision. Assuming FP32.
2025-08-02 21:16:49,289 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Unable to determine original precision. Assuming FP32.
2025-08-02 21:16:49,298 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Unable to determine original precision. Assuming FP32.
2025-08-02 21:16:49,302 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Unable to determine original precision. Assuming FP32.
2025-08-02 21:16:49,311 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Unable to determine original precision. Assuming FP32.
2025-08-02 21:16:49,316 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Unable to determine original precision. Assuming FP32.
2025-08-02 21:16:49,348 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Unable to determine original precision. Assuming FP32.
2025-08-02 21:16:49,357 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Unable to determine original precision. Assuming FP32.
2025-08-02 21:16:49,361 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Unable to determine original precision. Assuming FP32.
2025-08-02 21:16:49,370 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Unable to determine original precision. Assuming FP32.
2025-08-02 21:16:49,375 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Unable to determine original precision. Assuming FP32.
2025-08-02 21:16:49,423 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Unable to determine original precision. Assuming FP32.
2025-08-02 21:16:49,432 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Unable to determine original precision. Assuming FP32.
2025-08-02 21:16:49,435 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Unable to determine original precision. Assuming FP32.
2025-08-02 21:16:49,445 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Unable to determine original precision. Assuming FP32.
2025-08-02 21:16:49,450 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Unable to determine original precision. Assuming FP32.
2025-08-02 21:16:49,467 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Unable to determine original precision. Assuming FP32.
2025-08-02 21:16:49,476 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Unable to determine original precision. Assuming FP32.
2025-08-02 21:16:49,479 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Unable to determine original precision. Assuming FP32.
2025-08-02 21:16:49,491 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Unable to determine original precision. Assuming FP32.
2025-08-02 21:16:49,497 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Unable to determine original precision. Assuming FP32.
2025-08-02 21:16:49,516 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Unable to determine original precision. Assuming FP32.
2025-08-02 21:16:49,525 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Unable to determine original precision. Assuming FP32.
2025-08-02 21:16:49,528 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Unable to determine original precision. Assuming FP32.
2025-08-02 21:16:49,539 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Unable to determine original precision. Assuming FP32.
2025-08-02 21:16:49,545 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Unable to determine original precision. Assuming FP32.
2025-08-02 21:16:49,577 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Unable to determine original precision. Assuming FP32.
2025-08-02 21:16:49,587 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Unable to determine original precision. Assuming FP32.
2025-08-02 21:16:49,590 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Unable to determine original precision. Assuming FP32.
2025-08-02 21:16:49,601 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Unable to determine original precision. Assuming FP32.
2025-08-02 21:16:49,608 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Unable to determine original precision. Assuming FP32.
2025-08-02 21:16:49,655 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Unable to determine original precision. Assuming FP32.
2025-08-02 21:16:49,665 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Unable to determine original precision. Assuming FP32.
2025-08-02 21:16:49,669 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Unable to determine original precision. Assuming FP32.
2025-08-02 21:16:49,679 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Unable to determine original precision. Assuming FP32.
2025-08-02 21:16:49,686 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Unable to determine original precision. Assuming FP32.
2025-08-02 21:16:49,719 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Unable to determine original precision. Assuming FP32.
2025-08-02 21:16:49,728 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Unable to determine original precision. Assuming FP32.
2025-08-02 21:16:49,732 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Unable to determine original precision. Assuming FP32.
2025-08-02 21:16:49,742 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Unable to determine original precision. Assuming FP32.
2025-08-02 21:16:49,749 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Unable to determine original precision. Assuming FP32.
2025-08-02 21:16:49,795 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Unable to determine original precision. Assuming FP32.
2025-08-02 21:16:49,806 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Unable to determine original precision. Assuming FP32.
2025-08-02 21:16:49,810 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Unable to determine original precision. Assuming FP32.
2025-08-02 21:16:49,820 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Unable to determine original precision. Assuming FP32.
2025-08-02 21:16:49,827 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Unable to determine original precision. Assuming FP32.
2025-08-02 21:16:49,859 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Unable to determine original precision. Assuming FP32.
2025-08-02 21:16:49,868 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Unable to determine original precision. Assuming FP32.
2025-08-02 21:16:49,872 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Unable to determine original precision. Assuming FP32.
2025-08-02 21:16:49,883 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Unable to determine original precision. Assuming FP32.
2025-08-02 21:16:49,899 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Unable to determine original precision. Assuming FP32.
2025-08-02 21:16:49,945 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Unable to determine original precision. Assuming FP32.
2025-08-02 21:16:49,954 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Unable to determine original precision. Assuming FP32.
2025-08-02 21:16:49,958 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Unable to determine original precision. Assuming FP32.
2025-08-02 21:16:49,969 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Unable to determine original precision. Assuming FP32.
2025-08-02 21:16:49,975 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Unable to determine original precision. Assuming FP32.
2025-08-02 21:16:50,008 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Unable to determine original precision. Assuming FP32.
2025-08-02 21:16:50,017 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Unable to determine original precision. Assuming FP32.
2025-08-02 21:16:50,021 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Unable to determine original precision. Assuming FP32.
2025-08-02 21:16:50,031 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Unable to determine original precision. Assuming FP32.
2025-08-02 21:16:50,037 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Unable to determine original precision. Assuming FP32.
2025-08-02 21:16:50,069 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Unable to determine original precision. Assuming FP32.
2025-08-02 21:16:50,078 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Unable to determine original precision. Assuming FP32.
2025-08-02 21:16:50,082 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Unable to determine original precision. Assuming FP32.
2025-08-02 21:16:50,092 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Unable to determine original precision. Assuming FP32.
2025-08-02 21:16:50,098 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Unable to determine original precision. Assuming FP32.
2025-08-02 21:16:50,144 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Unable to determine original precision. Assuming FP32.
2025-08-02 21:16:50,153 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Unable to determine original precision. Assuming FP32.
2025-08-02 21:16:50,157 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Unable to determine original precision. Assuming FP32.
2025-08-02 21:16:50,167 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Unable to determine original precision. Assuming FP32.
2025-08-02 21:16:50,173 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Unable to determine original precision. Assuming FP32.
2025-08-02 21:16:50,192 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Unable to determine original precision. Assuming FP32.
2025-08-02 21:16:50,202 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Unable to determine original precision. Assuming FP32.
2025-08-02 21:16:50,205 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Unable to determine original precision. Assuming FP32.
2025-08-02 21:16:50,216 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Unable to determine original precision. Assuming FP32.
2025-08-02 21:16:50,231 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Unable to determine original precision. Assuming FP32.
2025-08-02 21:16:50,261 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Unable to determine original precision. Assuming FP32.
2025-08-02 21:16:50,270 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Unable to determine original precision. Assuming FP32.
2025-08-02 21:16:50,274 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Unable to determine original precision. Assuming FP32.
2025-08-02 21:16:50,284 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Unable to determine original precision. Assuming FP32.
2025-08-02 21:16:50,291 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Unable to determine original precision. Assuming FP32.
2025-08-02 21:16:50,308 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Unable to determine original precision. Assuming FP32.
2025-08-02 21:16:50,318 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Unable to determine original precision. Assuming FP32.
2025-08-02 21:16:50,322 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Unable to determine original precision. Assuming FP32.
2025-08-02 21:16:50,334 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Unable to determine original precision. Assuming FP32.
2025-08-02 21:16:50,349 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Unable to determine original precision. Assuming FP32.
2025-08-02 21:16:50,396 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Unable to determine original precision. Assuming FP32.
2025-08-02 21:16:50,407 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Unable to determine original precision. Assuming FP32.
2025-08-02 21:16:50,410 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Unable to determine original precision. Assuming FP32.
2025-08-02 21:16:50,421 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Unable to determine original precision. Assuming FP32.
2025-08-02 21:16:50,429 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Unable to determine original precision. Assuming FP32.
2025-08-02 21:16:50,464 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Unable to determine original precision. Assuming FP32.
2025-08-02 21:16:50,473 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Unable to determine original precision. Assuming FP32.
2025-08-02 21:16:50,477 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Unable to determine original precision. Assuming FP32.
2025-08-02 21:16:50,489 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Unable to determine original precision. Assuming FP32.
2025-08-02 21:16:50,495 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Unable to determine original precision. Assuming FP32.
2025-08-02 21:16:50,542 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Unable to determine original precision. Assuming FP32.
2025-08-02 21:16:50,551 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Unable to determine original precision. Assuming FP32.
2025-08-02 21:16:50,554 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Unable to determine original precision. Assuming FP32.
2025-08-02 21:16:50,566 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Unable to determine original precision. Assuming FP32.
2025-08-02 21:16:50,572 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Unable to determine original precision. Assuming FP32.
2025-08-02 21:16:50,583 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Unable to determine original precision. Assuming FP32.
2025-08-02 21:16:50,593 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Unable to determine original precision. Assuming FP32.
2025-08-02 21:16:50,597 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Unable to determine original precision. Assuming FP32.
2025-08-02 21:16:50,608 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Unable to determine original precision. Assuming FP32.
2025-08-02 21:16:50,615 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Unable to determine original precision. Assuming FP32.
2025-08-02 21:16:50,625 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Unable to determine original precision. Assuming FP32.
2025-08-02 21:16:50,635 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Unable to determine original precision. Assuming FP32.
2025-08-02 21:16:50,639 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Unable to determine original precision. Assuming FP32.
2025-08-02 21:16:50,650 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Unable to determine original precision. Assuming FP32.
2025-08-02 21:16:50,656 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Unable to determine original precision. Assuming FP32.
2025-08-02 21:16:50,702 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Unable to determine original precision. Assuming FP32.
2025-08-02 21:16:50,710 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Unable to determine original precision. Assuming FP32.
2025-08-02 21:16:50,714 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Unable to determine original precision. Assuming FP32.
2025-08-02 21:16:50,725 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Unable to determine original precision. Assuming FP32.
2025-08-02 21:16:50,732 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Unable to determine original precision. Assuming FP32.
2025-08-02 21:16:50,735 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Unable to determine original precision. Assuming FP32.






2025-08-03 13:27:10,816 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] 2025-08-03 13:27:10,816 - INFO - Saved image 1/1000 to /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/results/qua2sedimo_w4a8/fid_samples/sample_00000.png
2025-08-03 13:27:10,834 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] 2025-08-03 13:27:10,834 - INFO - Saved image 2/1000 to /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/results/qua2sedimo_w4a8/fid_samples/sample_00001.png
2025-08-03 13:27:10,852 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] 2025-08-03 13:27:10,851 - INFO - Saved image 3/1000 to /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/results/qua2sedimo_w4a8/fid_samples/sample_00002.png
2025-08-03 13:27:10,869 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] 2025-08-03 13:27:10,869 - INFO - Saved image 4/1000 to /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/results/qua2sedimo_w4a8/fid_samples/sample_00003.png
2025-08-03 13:27:10,886 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] 2025-08-03 13:27:10,886 - INFO - Saved image 5/1000 to /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/results/qua2sedimo_w4a8/fid_samples/sample_00004.png
2025-08-03 13:27:10,902 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] 2025-08-03 13:27:10,902 - INFO - Saved image 6/1000 to /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/results/qua2sedimo_w4a8/fid_samples/sample_00005.png
2025-08-03 13:27:10,920 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] 2025-08-03 13:27:10,919 - INFO - Saved image 7/1000 to /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/results/qua2sedimo_w4a8/fid_samples/sample_00006.png
2025-08-03 13:27:10,936 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] 2025-08-03 13:27:10,936 - INFO - Saved image 8/1000 to /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/results/qua2sedimo_w4a8/fid_samples/sample_00007.png
2025-08-03 13:27:10,953 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] 2025-08-03 13:27:10,953 - INFO - Saved image 9/1000 to /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/results/qua2sedimo_w4a8/fid_samples/sample_00008.png
2025-08-03 13:27:10,971 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] 2025-08-03 13:27:10,971 - INFO - Saved image 10/1000 to /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/results/qua2sedimo_w4a8/fid_samples/sample_00009.png
2025-08-03 13:27:10,987 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] 2025-08-03 13:27:10,987 - INFO - Saved image 11/1000 to /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/results/qua2sedimo_w4a8/fid_samples/sample_00010.png
2025-08-03 13:27:11,004 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] 2025-08-03 13:27:11,004 - INFO - Saved image 12/1000 to /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/results/qua2sedimo_w4a8/fid_samples/sample_00011.png
2025-08-03 13:27:11,021 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] 2025-08-03 13:27:11,021 - INFO - Saved image 13/1000 to /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/results/qua2sedimo_w4a8/fid_samples/sample_00012.png
2025-08-03 13:27:11,039 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] 2025-08-03 13:27:11,039 - INFO - Saved image 14/1000 to /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/results/qua2sedimo_w4a8/fid_samples/sample_00013.png
2025-08-03 13:27:11,055 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] 2025-08-03 13:27:11,054 - INFO - Saved image 15/1000 to /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/results/qua2sedimo_w4a8/fid_samples/sample_00014.png
2025-08-03 13:27:11,071 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] 2025-08-03 13:27:11,071 - INFO - Saved image 16/1000 to /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/results/qua2sedimo_w4a8/fid_samples/sample_00015.png
2025-08-03 13:27:11,088 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] 2025-08-03 13:27:11,088 - INFO - Saved image 17/1000 to /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/results/qua2sedimo_w4a8/fid_samples/sample_00016.png
2025-08-03 13:27:11,104 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] 2025-08-03 13:27:11,103 - INFO - Saved image 18/1000 to /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/results/qua2sedimo_w4a8/fid_samples/sample_00017.png
2025-08-03 13:27:11,121 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] 2025-08-03 13:27:11,121 - INFO - Saved image 19/1000 to /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/results/qua2sedimo_w4a8/fid_samples/sample_00018.png
2025-08-03 13:27:11,139 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] 2025-08-03 13:27:11,139 - INFO - Saved image 20/1000 to /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/results/qua2sedimo_w4a8/fid_samples/sample_00019.png
2025-08-03 13:27:11,154 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] 2025-08-03 13:27:11,153 - INFO - Saved image 21/1000 to /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/results/qua2sedimo_w4a8/fid_samples/sample_00020.png
2025-08-03 13:27:11,171 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] 2025-08-03 13:27:11,171 - INFO - Saved image 22/1000 to /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/results/qua2sedimo_w4a8/fid_samples/sample_00021.png
2025-08-03 13:27:11,188 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] 2025-08-03 13:27:11,187 - INFO - Saved image 23/1000 to /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/results/qua2sedimo_w4a8/fid_samples/sample_00022.png
2025-08-03 13:27:11,204 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] 2025-08-03 13:27:11,204 - INFO - Saved image 24/1000 to /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/results/qua2sedimo_w4a8/fid_samples/sample_00023.png
2025-08-03 13:27:11,221 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] 2025-08-03 13:27:11,221 - INFO - Saved image 25/1000 to /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/results/qua2sedimo_w4a8/fid_samples/sample_00024.png
2025-08-03 13:27:11,238 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] 2025-08-03 13:27:11,238 - INFO - Saved image 26/1000 to /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/results/qua2sedimo_w4a8/fid_samples/sample_00025.png
2025-08-03 13:27:11,256 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] 2025-08-03 13:27:11,255 - INFO - Saved image 27/1000 to /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/results/qua2sedimo_w4a8/fid_samples/sample_00026.png
2025-08-03 13:27:11,273 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] 2025-08-03 13:27:11,273 - INFO - Saved image 28/1000 to /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/results/qua2sedimo_w4a8/fid_samples/sample_00027.png
2025-08-03 13:27:11,290 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] 2025-08-03 13:27:11,290 - INFO - Saved image 29/1000 to /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/results/qua2sedimo_w4a8/fid_samples/sample_00028.png
2025-08-03 13:27:11,307 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] 2025-08-03 13:27:11,307 - INFO - Saved image 30/1000 to /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/results/qua2sedimo_w4a8/fid_samples/sample_00029.png
2025-08-03 13:27:11,324 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] 2025-08-03 13:27:11,324 - INFO - Saved image 31/1000 to /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/results/qua2sedimo_w4a8/fid_samples/sample_00030.png
2025-08-03 13:27:11,341 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] 2025-08-03 13:27:11,341 - INFO - Saved image 32/1000 to /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/results/qua2sedimo_w4a8/fid_samples/sample_00031.png
2025-08-03 13:27:11,357 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] 2025-08-03 13:27:11,357 - INFO - Saved image 33/1000 to /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/results/qua2sedimo_w4a8/fid_samples/sample_00032.png
2025-08-03 13:27:11,374 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] 2025-08-03 13:27:11,373 - INFO - Saved image 34/1000 to /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/results/qua2sedimo_w4a8/fid_samples/sample_00033.png
2025-08-03 13:27:11,390 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] 2025-08-03 13:27:11,390 - INFO - Saved image 35/1000 to /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/results/qua2sedimo_w4a8/fid_samples/sample_00034.png
2025-08-03 13:27:11,408 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] 2025-08-03 13:27:11,408 - INFO - Saved image 36/1000 to /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/results/qua2sedimo_w4a8/fid_samples/sample_00035.png
2025-08-03 13:27:11,424 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] 2025-08-03 13:27:11,424 - INFO - Saved image 37/1000 to /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/results/qua2sedimo_w4a8/fid_samples/sample_00036.png
2025-08-03 13:27:11,442 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] 2025-08-03 13:27:11,442 - INFO - Saved image 38/1000 to /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/results/qua2sedimo_w4a8/fid_samples/sample_00037.png
2025-08-03 13:27:11,460 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] 2025-08-03 13:27:11,460 - INFO - Saved image 39/1000 to /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/results/qua2sedimo_w4a8/fid_samples/sample_00038.png
2025-08-03 13:27:11,476 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] 2025-08-03 13:27:11,476 - INFO - Saved image 40/1000 to /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/results/qua2sedimo_w4a8/fid_samples/sample_00039.png
2025-08-03 13:27:11,493 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] 2025-08-03 13:27:11,493 - INFO - Saved image 41/1000 to /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/results/qua2sedimo_w4a8/fid_samples/sample_00040.png
2025-08-03 13:27:11,510 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] 2025-08-03 13:27:11,510 - INFO - Saved image 42/1000 to /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/results/qua2sedimo_w4a8/fid_samples/sample_00041.png
2025-08-03 13:27:11,527 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] 2025-08-03 13:27:11,527 - INFO - Saved image 43/1000 to /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/results/qua2sedimo_w4a8/fid_samples/sample_00042.png
2025-08-03 13:27:11,544 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] 2025-08-03 13:27:11,544 - INFO - Saved image 44/1000 to /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/results/qua2sedimo_w4a8/fid_samples/sample_00043.png
2025-08-03 13:27:11,562 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] 2025-08-03 13:27:11,562 - INFO - Saved image 45/1000 to /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/results/qua2sedimo_w4a8/fid_samples/sample_00044.png
2025-08-03 13:27:11,579 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] 2025-08-03 13:27:11,579 - INFO - Saved image 46/1000 to /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/results/qua2sedimo_w4a8/fid_samples/sample_00045.png
2025-08-03 13:27:11,596 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] 2025-08-03 13:27:11,596 - INFO - Saved image 47/1000 to /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/results/qua2sedimo_w4a8/fid_samples/sample_00046.png
2025-08-03 13:27:11,612 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] 2025-08-03 13:27:11,612 - INFO - Saved image 48/1000 to /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/results/qua2sedimo_w4a8/fid_samples/sample_00047.png
2025-08-03 13:27:11,628 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] 2025-08-03 13:27:11,628 - INFO - Saved image 49/1000 to /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/results/qua2sedimo_w4a8/fid_samples/sample_00048.png
2025-08-03 13:27:11,646 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] 2025-08-03 13:27:11,646 - INFO - Saved image 50/1000 to /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/results/qua2sedimo_w4a8/fid_samples/sample_00049.png
2025-08-03 13:27:11,663 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] 2025-08-03 13:27:11,663 - INFO - Saved image 51/1000 to /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/results/qua2sedimo_w4a8/fid_samples/sample_00050.png
2025-08-03 13:27:11,680 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] 2025-08-03 13:27:11,679 - INFO - Saved image 52/1000 to /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/results/qua2sedimo_w4a8/fid_samples/sample_00051.png
2025-08-03 13:27:11,696 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] 2025-08-03 13:27:11,696 - INFO - Saved image 53/1000 to /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/results/qua2sedimo_w4a8/fid_samples/sample_00052.png
2025-08-03 13:27:11,713 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] 2025-08-03 13:27:11,713 - INFO - Saved image 54/1000 to /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/results/qua2sedimo_w4a8/fid_samples/sample_00053.png
2025-08-03 13:27:11,730 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] 2025-08-03 13:27:11,730 - INFO - Saved image 55/1000 to /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/results/qua2sedimo_w4a8/fid_samples/sample_00054.png
2025-08-03 13:27:11,747 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] 2025-08-03 13:27:11,747 - INFO - Saved image 56/1000 to /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/results/qua2sedimo_w4a8/fid_samples/sample_00055.png
2025-08-03 13:27:11,764 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] 2025-08-03 13:27:11,764 - INFO - Saved image 57/1000 to /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/results/qua2sedimo_w4a8/fid_samples/sample_00056.png
2025-08-03 13:27:11,780 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] 2025-08-03 13:27:11,780 - INFO - Saved image 58/1000 to /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/results/qua2sedimo_w4a8/fid_samples/sample_00057.png
2025-08-03 13:27:11,797 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] 2025-08-03 13:27:11,797 - INFO - Saved image 59/1000 to /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/results/qua2sedimo_w4a8/fid_samples/sample_00058.png
2025-08-03 13:27:11,812 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] 2025-08-03 13:27:11,812 - INFO - Saved image 60/1000 to /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/results/qua2sedimo_w4a8/fid_samples/sample_00059.png
2025-08-03 13:27:11,829 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] 2025-08-03 13:27:11,829 - INFO - Saved image 61/1000 to /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/results/qua2sedimo_w4a8/fid_samples/sample_00060.png
2025-08-03 13:27:11,846 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Traceback (most recent call last):
2025-08-03 13:27:11,847 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] File "/home/<USER>/.conda/envs/dquant-rag2/lib/python3.10/site-packages/PIL/ImageFile.py", line 554, in _save
2025-08-03 13:27:11,847 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] fh = fp.fileno()
2025-08-03 13:27:11,847 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] AttributeError: '_idat' object has no attribute 'fileno'
2025-08-03 13:27:11,847 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] During handling of the above exception, another exception occurred:
2025-08-03 13:27:11,847 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Traceback (most recent call last):
2025-08-03 13:27:11,847 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] File "/home/<USER>/.conda/envs/dquant-rag2/lib/python3.10/site-packages/PIL/Image.py", line 2605, in save
2025-08-03 13:27:11,847 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] save_handler(self, fp, filename)
2025-08-03 13:27:11,847 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] File "/home/<USER>/.conda/envs/dquant-rag2/lib/python3.10/site-packages/PIL/PngImagePlugin.py", line 1488, in _save
2025-08-03 13:27:11,848 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] ImageFile._save(
2025-08-03 13:27:11,848 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] File "/home/<USER>/.conda/envs/dquant-rag2/lib/python3.10/site-packages/PIL/ImageFile.py", line 558, in _save
2025-08-03 13:27:11,848 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] _encode_tile(im, fp, tile, bufsize, None, exc)
2025-08-03 13:27:11,848 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] File "/home/<USER>/.conda/envs/dquant-rag2/lib/python3.10/site-packages/PIL/ImageFile.py", line 585, in _encode_tile
--- Logging error ---
Traceback (most recent call last):
  File "/home/<USER>/.conda/envs/dquant-rag2/lib/python3.10/logging/__init__.py", line 1104, in emit
    self.flush()
  File "/home/<USER>/.conda/envs/dquant-rag2/lib/python3.10/logging/__init__.py", line 1084, in flush
    self.stream.flush()
OSError: [Errno 28] No space left on device
Call stack:
  File "/home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/run_benchmark.py", line 1246, in <module>
    main()
  File "/home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/run_benchmark.py", line 1235, in main
    all_results[task['method']] = task['func'](env_vars)
  File "/home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/run_benchmark.py", line 584, in benchmark_qua2sedimo
    run_script([str(custom_infer_script)] + [
  File "/home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/run_benchmark.py", line 121, in run_script
    logger.info(f"[SUBPROCESS] {line}")
Message: '[SUBPROCESS] File "/home/<USER>/.conda/envs/dquant-rag2/lib/python3.10/site-packages/PIL/ImageFile.py", line 585, in _encode_tile'
Arguments: ()
2025-08-03 13:27:11,850 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] fp.write(data)
2025-08-03 13:27:11,850 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] File "/home/<USER>/.conda/envs/dquant-rag2/lib/python3.10/site-packages/PIL/PngImagePlugin.py", line 1132, in write
2025-08-03 13:27:11,850 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] self.chunk(self.fp, b"IDAT", data)
2025-08-03 13:27:11,850 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] File "/home/<USER>/.conda/envs/dquant-rag2/lib/python3.10/site-packages/PIL/PngImagePlugin.py", line 1119, in putchunk
2025-08-03 13:27:11,850 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] fp.write(byte_data)
2025-08-03 13:27:11,851 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] OSError: [Errno 28] No space left on device
2025-08-03 13:27:11,851 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] During handling of the above exception, another exception occurred:
2025-08-03 13:27:11,851 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Traceback (most recent call last):
2025-08-03 13:27:11,851 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] File "/home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/results/qua2sedimo_w4a8/qua2sedimo_inference.py", line 274, in <module>
2025-08-03 13:27:11,851 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] main()
2025-08-03 13:27:11,851 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] File "/home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/results/qua2sedimo_w4a8/qua2sedimo_inference.py", line 268, in main
2025-08-03 13:27:11,851 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] img.save(img_path)
2025-08-03 13:27:11,851 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] File "/home/<USER>/.conda/envs/dquant-rag2/lib/python3.10/site-packages/PIL/Image.py", line 2608, in save
2025-08-03 13:27:11,851 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] fp.close()
2025-08-03 13:27:11,851 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] OSError: [Errno 28] No space left on device
2025-08-03 13:27:13,550 - [qua2sedimo-GPU7] - [ERROR] - Benchmark for qua2sedimo failed.
Traceback (most recent call last):
  File "/home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/run_benchmark.py", line 1235, in main
    all_results[task['method']] = task['func'](env_vars)
  File "/home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/run_benchmark.py", line 584, in benchmark_qua2sedimo
    run_script([str(custom_infer_script)] + [
  File "/home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/run_benchmark.py", line 127, in run_script
    raise subprocess.CalledProcessError(return_code, final_command, "\n".join(output_lines))
subprocess.CalledProcessError: Command '['python', '-u', '/home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/results/qua2sedimo_w4a8/qua2sedimo_inference.py', '--outdir', '/home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/results/qua2sedimo_w4a8/fid_samples', '--weight_bit', '4', '--multi_chkpt', '/home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/github-repos/Qua2SeDiMo/multiquantizers/dit_mq.pt', '--seed', '1234', '--n_imgs', '1000', '--res', '256', '--num_inference_steps', '50', '--dit_ckpt', '/home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/models/DiT-XL-2-256x256.pt', '--vae_path', '/home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/models/local_vae_model']' returned non-zero exit status 1.
2025-08-03 13:27:13,551 - [qua2sedimo-GPU7] - [WARNING] - No benchmark results were successfully generated.