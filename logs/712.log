2025-08-02 09:44:54,328 - [Main] - [INFO] - --- Phase 1: Preparing Environment ---
2025-08-02 09:44:54,328 - [Main] - [INFO] - Preparation tasks will use GPU: 7
2025-08-02 09:44:54,328 - [Main] - [INFO] - Using pretrained DiT model: /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/models/DiT-XL-2-256x256.pt
2025-08-02 09:44:54,328 - [Main] - [INFO] - Using local VAE model: /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/models/local_vae_model
2025-08-02 09:44:54,328 - [Main] - [INFO] - Reference images already exist (24 images), skipping generation.
2025-08-02 09:44:54,328 - [Main] - [INFO] - Running in Single-GPU mode.
2025-08-02 09:44:54,329 - [qua2sedimo-GPU7] - [INFO] - --- Starting benchmark for qua2sedimo on GPU 7 ---
2025-08-02 09:44:54,329 - [qua2sedimo-GPU7] - [INFO] - Qua2SeDiMo multiquantizer checkpoint already exists at /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/github-repos/Qua2SeDiMo/multiquantizers/dit_mq.pt, skipping generation step.
2025-08-02 09:44:54,329 - [qua2sedimo-GPU7] - [INFO] - Sampling for FID with Qua2SeDiMo...
2025-08-02 09:44:54,329 - [qua2sedimo-GPU7] - [INFO] - Executing in '/home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/github-repos/Qua2SeDiMo':
$ python -u /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/results/qua2sedimo_w4a8/qua2sedimo_inference.py --outdir /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/results/qua2sedimo_w4a8/fid_samples --weight_bit 4 --multi_chkpt /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/github-repos/Qua2SeDiMo/multiquantizers/dit_mq.pt --seed 1234 --n_imgs 1000 --res 256 --num_inference_steps 50 --dit_ckpt /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/models/DiT-XL-2-256x256.pt --vae_path /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/models/local_vae_model

2025-08-02 09:44:59,837 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Global seed set to 1234
2025-08-02 09:44:59,839 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] 2025-08-02 09:44:59,839 - INFO - Attempting to load local DiT model and VAE...
2025-08-02 09:44:59,839 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] 2025-08-02 09:44:59,839 - ERROR - Failed to load local models: Cannot import DiT modules
2025-08-02 09:44:59,839 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] 2025-08-02 09:44:59,839 - ERROR - Cannot proceed without local models in offline mode
2025-08-02 09:44:59,839 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Traceback (most recent call last):
2025-08-02 09:44:59,839 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] File "/home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/results/qua2sedimo_w4a8/qua2sedimo_inference.py", line 33, in load_dit_model_from_checkpoint
2025-08-02 09:44:59,840 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] from diffusion import create_diffusion
2025-08-02 09:44:59,840 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] ModuleNotFoundError: No module named 'diffusion'
2025-08-02 09:44:59,840 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] During handling of the above exception, another exception occurred:
2025-08-02 09:44:59,840 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Traceback (most recent call last):
2025-08-02 09:44:59,840 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] File "/home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/results/qua2sedimo_w4a8/qua2sedimo_inference.py", line 81, in main
2025-08-02 09:44:59,840 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] dit_model, diffusion = load_dit_model_from_checkpoint(opt.dit_ckpt, opt.res)
2025-08-02 09:44:59,840 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] File "/home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/results/qua2sedimo_w4a8/qua2sedimo_inference.py", line 37, in load_dit_model_from_checkpoint
2025-08-02 09:44:59,840 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] raise ImportError("Cannot import DiT modules")
2025-08-02 09:44:59,840 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] ImportError: Cannot import DiT modules
2025-08-02 09:44:59,841 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] During handling of the above exception, another exception occurred:
2025-08-02 09:44:59,841 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Traceback (most recent call last):
2025-08-02 09:44:59,841 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] File "/home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/results/qua2sedimo_w4a8/qua2sedimo_inference.py", line 219, in <module>
2025-08-02 09:44:59,841 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] main()
2025-08-02 09:44:59,841 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] File "/home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/results/qua2sedimo_w4a8/qua2sedimo_inference.py", line 95, in main
2025-08-02 09:44:59,841 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] raise RuntimeError(f"Failed to load required local models: {e}")
2025-08-02 09:44:59,841 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] RuntimeError: Failed to load required local models: Cannot import DiT modules
2025-08-02 09:45:00,735 - [qua2sedimo-GPU7] - [ERROR] - Benchmark for qua2sedimo failed.
Traceback (most recent call last):
  File "/home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/run_benchmark.py", line 1180, in main
    all_results[task['method']] = task['func'](env_vars)
  File "/home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/run_benchmark.py", line 584, in benchmark_qua2sedimo
    run_script([str(custom_infer_script)] + [
  File "/home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/run_benchmark.py", line 127, in run_script
    raise subprocess.CalledProcessError(return_code, final_command, "\n".join(output_lines))
subprocess.CalledProcessError: Command '['python', '-u', '/home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/results/qua2sedimo_w4a8/qua2sedimo_inference.py', '--outdir', '/home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/results/qua2sedimo_w4a8/fid_samples', '--weight_bit', '4', '--multi_chkpt', '/home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/github-repos/Qua2SeDiMo/multiquantizers/dit_mq.pt', '--seed', '1234', '--n_imgs', '1000', '--res', '256', '--num_inference_steps', '50', '--dit_ckpt', '/home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/models/DiT-XL-2-256x256.pt', '--vae_path', '/home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/models/local_vae_model']' returned non-zero exit status 1.
2025-08-02 09:45:00,735 - [qua2sedimo-GPU7] - [WARNING] - No benchmark results were successfully generated.