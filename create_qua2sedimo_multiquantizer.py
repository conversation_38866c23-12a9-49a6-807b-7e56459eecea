#!/usr/bin/env python3
"""
为Qua2SeDiMo创建multiquantizer checkpoint的脚本
使用本地DiT模型而不是HuggingFace在线模型
"""

import argparse
import os
import sys
import torch
import logging
from pathlib import Path

# 设置OpenBLAS环境变量以避免内存分配问题
os.environ['OPENBLAS_NUM_THREADS'] = '1'
os.environ['MKL_NUM_THREADS'] = '1'
os.environ['OMP_NUM_THREADS'] = '1'

# 设置OpenBLAS环境变量以避免内存分配问题
os.environ['OPENBLAS_NUM_THREADS'] = '1'
os.environ['MKL_NUM_THREADS'] = '1'
os.environ['OMP_NUM_THREADS'] = '1'

# 添加Qua2SeDiMo到Python路径
qua2sedimo_path = Path(__file__).parent / "github-repos" / "Qua2SeDiMo"
sys.path.insert(0, str(qua2sedimo_path))

from pytorch_lightning import seed_everything
from qdiff import QuantModelMultiQ

def load_dit_model_from_checkpoint(ckpt_path, image_size=256):
    """从本地checkpoint加载DiT模型"""
    # 添加DiTAS路径以使用其模型定义
    ditas_path = Path(__file__).parent / "github-repos" / "DiTAS"
    sys.path.insert(0, str(ditas_path))
    
    from models import DiT_XL_2
    
    # 创建DiT模型
    model = DiT_XL_2(input_size=image_size // 8)
    
    # 加载checkpoint
    state_dict = torch.load(ckpt_path, map_location="cpu")
    model.load_state_dict(state_dict)
    
    return model

def main():
    parser = argparse.ArgumentParser()
    parser.add_argument("--weight_bit", type=int, default=4, help="Weight quantization bits")
    parser.add_argument("--seed", type=int, default=1234, help="Random seed")
    parser.add_argument("--dit_ckpt", type=str, default="models/DiT-XL-2-256x256.pt", 
                       help="Path to DiT checkpoint")
    parser.add_argument("--image_size", type=int, default=256, help="Image size")
    parser.add_argument("--output_dir", type=str, default="github-repos/Qua2SeDiMo/multiquantizers",
                       help="Output directory for multiquantizer checkpoint")
    
    args = parser.parse_args()
    
    # 设置日志
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    logger = logging.getLogger(__name__)
    
    # 设置随机种子
    seed_everything(args.seed)
    
    # 创建输出目录
    output_dir = Path(args.output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    try:
        logger.info(f"Loading DiT model from {args.dit_ckpt}")
        
        # 加载DiT模型
        model = load_dit_model_from_checkpoint(args.dit_ckpt, args.image_size)
        model = model.to("cuda").eval()
        
        logger.info("Setting up quantization...")
        
        # 设置量化参数
        wq_params = {'n_bits': args.weight_bit, 'channel_wise': True, 'scale_method': 'mse'}
        aq_params = {'n_bits': 8, 'channel_wise': False, 'scale_method': 'mse', 'leaf_param': False}
        
        # 创建量化模型
        qnn = QuantModelMultiQ(
            model=model, weight_quant_params=wq_params, act_quant_params=aq_params,
            act_quant_mode="qdiff")
        qnn.to("cuda")
        qnn.eval()
        
        # 设置量化状态
        qnn.set_quant_state(weight_quant=True, act_quant=False)
        
        logger.info("Generating quantization configuration parameters...")
        logger.warning("This may take a while and require significant VRAM...")
        
        # 生成一些示例输入来初始化量化参数
        with torch.no_grad():
            # 创建示例输入
            batch_size = 2
            latent_size = args.image_size // 8
            x = torch.randn(batch_size, 4, latent_size, latent_size, device="cuda")
            t = torch.randint(0, 1000, (batch_size,), device="cuda")
            y = torch.randint(0, 1000, (batch_size,), device="cuda")
            
            # 运行前向传播以初始化量化参数
            logger.info("Running forward pass to initialize quantization parameters...")
            _ = qnn(x, t, y)
            
            logger.info("Running second forward pass to stabilize parameters...")
            _ = qnn(x, t, y)
        
        # 保存multiquantizer checkpoint
        output_path = output_dir / "dit_mq.pt"
        logger.info(f"Saving multiquantizer checkpoint to {output_path}")
        
        # 获取状态字典并进行必要的转换
        sd = qnn.state_dict()
        
        # 对MSE量化参数进行reshape（如果需要）
        for k, v in sd.items():
            if "quant_params.mse" in k and len(v.shape) > 1:
                old_shape = list(v.shape)
                if old_shape[0] % 2 == 0:  # 确保可以被2整除
                    new_shape = [2, old_shape[0] // 2] + old_shape[1:]
                    v = v.reshape(new_shape)
                    sd[k] = v
        
        torch.save(sd, output_path)
        
        logger.info(f"Successfully created multiquantizer checkpoint at {output_path}")
        logger.info(f"Checkpoint size: {output_path.stat().st_size / (1024*1024):.2f} MB")
        
    except Exception as e:
        logger.error(f"Failed to create multiquantizer checkpoint: {e}")
        raise e

if __name__ == "__main__":
    main()
