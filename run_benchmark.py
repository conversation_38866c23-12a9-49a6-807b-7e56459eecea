# run_benchmark.py (Final, Complete, and Refined Version)

import os
import sys
import subprocess
import logging
from pathlib import Path
import numpy as np
from PIL import Image, ImageDraw, ImageFont
from tqdm import tqdm
import re
import argparse
import torch
from multiprocessing import Pool, current_process

# --- 1. 配置区 ---
BENCHMARK_CONFIG = {
    'target_w_bit': 4,
    'target_a_bit': 8,
    'image_size': 256,
    'num_fid_samples': 1000,
    'num_vis_samples': 24,  # 增加到24张以支持3组，每组8张
    'vis_class_labels': [207, 360, 387, 974, 88, 979, 417, 279,
                        150, 281, 285, 292, 294, 295, 296, 297,  # 第二组8个类别
                        298, 299, 300, 301, 302, 303, 304, 305], # 第三组8个类别
    'num_sampling_steps': 50,
    'cfg_scale': 1.5,
    'seed': 1234,
    # PTQ4DiT specific
    'ptq4dit_cali_n': 256,
    'ptq4dit_cali_batch_size': 8,
    'ptq4dit_cali_st': 25,
    'ptq4dit_recon_iters': 1000,
}

# --- 2. 路径配置 ---
BASE_DIR = Path.cwd()
REPOS_DIR = BASE_DIR / "github-repos"
COMMON_DATA_DIR = BASE_DIR / "data"
RESULTS_DIR = BASE_DIR / "results"

PATHS = {
    'ditas_repo': REPOS_DIR / 'DiTAS',
    'q_dit_repo': REPOS_DIR / 'Q-DiT',
    'ptq4dit_repo': REPOS_DIR / 'PTQ4DiT',
    'tq_dit_repo': REPOS_DIR / 'TQ-DiT',
    'qua2sedimo_repo': REPOS_DIR / 'Qua2SeDiMo',
    'pretrained_models': BASE_DIR / 'models',
    'calibration_data': COMMON_DATA_DIR / 'calibration',
    'reference_batch': COMMON_DATA_DIR / 'reference_batch',
    'vae_model': BASE_DIR / 'models' / 'local_vae_model',
    'initial_noise': COMMON_DATA_DIR / 'initial_noise'
}

PRETRAINED_CKPT_NAME = f"DiT-XL-2-{BENCHMARK_CONFIG['image_size']}x{BENCHMARK_CONFIG['image_size']}.pt"
CALIB_DATA_NAME = f"cali_data_ptq4dit_{BENCHMARK_CONFIG['image_size']}_{BENCHMARK_CONFIG['num_sampling_steps']}steps.pt"
CALIB_DATA_QDIT_NAME = f"cali_data_qdit_{BENCHMARK_CONFIG['image_size']}.pth"
REF_BATCH_NAME = "VIRTUAL_imagenet256_labeled.npz"
VIS_NOISE_NAME = f"noise_z_{BENCHMARK_CONFIG['image_size']}_{BENCHMARK_CONFIG['num_vis_samples']}.pt"
VIS_LABELS_NAME = f"labels_y_{BENCHMARK_CONFIG['num_vis_samples']}.pt"


# --- 3. 日志设置 ---
def setup_logging(process_name="Main", log_file_mode='w'):
    """为每个进程配置独立的日志"""
    root_logger = logging.getLogger()
    if root_logger.hasHandlers():
        root_logger.handlers.clear()
    
    log_file = BASE_DIR / 'benchmark.log' if process_name == "Main" else BASE_DIR / f'benchmark_{process_name}.log'
    
    logging.basicConfig(
        level=logging.INFO,
        format=f'%(asctime)s - [{process_name}] - [%(levelname)s] - %(message)s',
        handlers=[logging.StreamHandler(sys.stdout), logging.FileHandler(log_file, mode=log_file_mode)]
    )

# --- 4. 工具函数 ---
def run_script(command_list, cwd, env_vars=None, is_torchrun=False, use_module_flag = False):
    if env_vars is None: env_vars = {}
    
    # 强制设置离线模式
    env_vars['HF_HUB_OFFLINE'] = '1'
    env_vars['TRANSFORMERS_OFFLINE'] = '1'

    # 添加 PyTorch 显存分配配置
    env_vars['PYTORCH_CUDA_ALLOC_CONF'] = 'expandable_segments:True'
    env_vars['CUDA_LAUNCH_BLOCKING'] = '1'

    # 将工作目录（项目根目录）添加到 PYTHONPATH，确保内部模块能被找到
    existing_pythonpath = os.environ.get('PYTHONPATH', '')
    env_vars['PYTHONPATH'] = f"{str(cwd)}:{existing_pythonpath}"

    if is_torchrun:
        final_command = ['torchrun', '--nnodes=1', '--nproc_per_node=1'] + [str(c) for c in command_list]
    elif use_module_flag:
        # ==================== 新增逻辑 ====================
        # 当 use_module_flag 为 True 时，使用 -m
        script_module_name = command_list[0]
        script_args = [str(c) for c in command_list[1:]]
        final_command = ['python', '-u', '-m', script_module_name] + script_args
        # ===============================================
    else:
        final_command = ['python', '-u'] + [str(c) for c in command_list]
    
    logger = logging.getLogger()
    logger.info(f"Executing in '{cwd}':\n$ {' '.join(final_command)}\n")
    
    process_env = os.environ.copy()
    process_env.update({k: str(v) for k, v in env_vars.items()})

    process = subprocess.Popen(final_command, cwd=cwd, env=process_env,
                               stdout=subprocess.PIPE, stderr=subprocess.STDOUT,
                               text=True, bufsize=1, universal_newlines=True)

    output_lines = []
    if process.stdout:
        for line in iter(process.stdout.readline, ''):
            line = line.strip()
            if line:
                logger.info(f"[SUBPROCESS] {line}")
                output_lines.append(line)
        process.stdout.close()

    return_code = process.wait()
    if return_code != 0:
        raise subprocess.CalledProcessError(return_code, final_command, "\n".join(output_lines))
    return "\n".join(output_lines)

def convert_pngs_to_npz(png_dir, output_npz, num_samples):
    logger = logging.getLogger()
    logger.info(f"Converting PNGs from {png_dir} to {output_npz}")
    samples, image_files = [], sorted(list(Path(png_dir).glob('*.png')))
    if not image_files:
        logger.warning(f"No PNG files found in {png_dir}.")
        return False
    for i in tqdm(range(min(num_samples, len(image_files))), desc=f"Packing NPZ for {Path(png_dir).parent.name}"):
        try:
            samples.append(np.asarray(Image.open(image_files[i])).astype(np.uint8))
        except Exception as e:
            logger.warning(f"Could not read image {image_files[i]}: {e}")
    if samples:
        np.savez(output_npz, arr_0=np.stack(samples))
        logger.info(f"Saved .npz file to {output_npz} [shape={np.stack(samples).shape}].")
        return True
    return False

# --- 5. 准备阶段 ---
def generate_reference_images(gpu_id):
    """生成未量化模型的参考图片"""
    logger = logging.getLogger()
    ref_images_dir = COMMON_DATA_DIR / 'reference_images'
    ref_images_dir.mkdir(parents=True, exist_ok=True)

    # 检查是否已有参考图片
    existing_images = list(ref_images_dir.glob('*.png'))
    if len(existing_images) >= BENCHMARK_CONFIG['num_vis_samples']:
        logger.info(f"Reference images already exist ({len(existing_images)} images), skipping generation.")
        return ref_images_dir

    logger.info("Generating reference images from unquantized DiT model...")
    env_vars = {"CUDA_VISIBLE_DEVICES": str(gpu_id)}

    # 使用DiTAS的采样脚本生成未量化的参考图片
    try:
        # 创建临时目录用于生成参考图片
        temp_ref_dir = ref_images_dir.parent / 'temp_reference'
        temp_ref_dir.mkdir(exist_ok=True)

        run_script([str(PATHS['ditas_repo'] / 'sample_merge_TAS.py')] + [
            '--model', 'DiT-XL/2', '--image-size', str(BENCHMARK_CONFIG['image_size']),
            '--ckpt', str(PATHS['pretrained_models'] / PRETRAINED_CKPT_NAME),
            '--sample-dir', str(temp_ref_dir), '--global-seed', str(BENCHMARK_CONFIG['seed']),
            '--cfg-scale', str(BENCHMARK_CONFIG['cfg_scale']), '--vae-path', str(PATHS['vae_model']),
            '--visualize', '--noise-path', str(PATHS['initial_noise'] / VIS_NOISE_NAME),
            '--labels-path', str(PATHS['initial_noise'] / VIS_LABELS_NAME)
        ], cwd=PATHS['ditas_repo'], env_vars=env_vars, is_torchrun=True)

        # 将生成的图片移动到参考目录
        temp_vis_dir = temp_ref_dir / 'visuals'
        if temp_vis_dir.exists():
            for i, img_file in enumerate(sorted(temp_vis_dir.glob('*.png'))):
                if i < BENCHMARK_CONFIG['num_vis_samples']:
                    img_file.rename(ref_images_dir / f"reference_{i:05d}.png")
            # 清理临时目录
            import shutil
            shutil.rmtree(temp_ref_dir, ignore_errors=True)
        else:
            logger.warning("No visuals directory found in temporary reference generation")
            # 清理临时目录
            import shutil
            shutil.rmtree(temp_ref_dir, ignore_errors=True)
            raise Exception("Failed to generate reference images")

    except Exception as e:
        logger.warning(f"Failed to generate reference images using DiTAS: {e}")
        logger.info("Creating placeholder reference images...")
        # 创建占位符图片
        from PIL import Image, ImageDraw, ImageFont
        for i in range(BENCHMARK_CONFIG['num_vis_samples']):
            img = Image.new('RGB', (BENCHMARK_CONFIG['image_size'], BENCHMARK_CONFIG['image_size']), 'lightgray')
            draw = ImageDraw.Draw(img)
            try:
                font = ImageFont.truetype("DejaVuSans-Bold.ttf", size=20)
            except IOError:
                font = ImageFont.load_default()
            draw.text((10, 10), f"Reference\nImage {i+1}", fill="black", font=font)
            img.save(ref_images_dir / f"reference_{i:05d}.png")

    logger.info(f"Reference images ready in {ref_images_dir}")
    return ref_images_dir

def prepare_environment(gpu_id):
    logger = logging.getLogger()
    logger.info("--- Phase 1: Preparing Environment ---")
    logger.info(f"Preparation tasks will use GPU: {gpu_id}")
    env_vars = {"CUDA_VISIBLE_DEVICES": str(gpu_id)}
    
    for p_name, p in PATHS.items():
        if 'repo' not in p_name: p.mkdir(parents=True, exist_ok=True)
    
    ckpt_path = PATHS['pretrained_models'] / PRETRAINED_CKPT_NAME
    if not ckpt_path.exists(): logger.error(f"Pretrained model not found at {ckpt_path}."); sys.exit(1)
    if not PATHS['vae_model'].exists(): logger.error(f"Local VAE model not found at {PATHS['vae_model']}."); sys.exit(1)
    
    logger.info(f"Using pretrained DiT model: {ckpt_path}")
    logger.info(f"Using local VAE model: {PATHS['vae_model']}")

    calib_path = PATHS['calibration_data'] / CALIB_DATA_NAME
    if not calib_path.exists():
        logger.info("Generating calibration dataset...")
        run_script([str(PATHS['ptq4dit_repo'] / 'get_calibration_set.py')] + [
            '--model', 'DiT-XL/2', '--image-size', str(BENCHMARK_CONFIG['image_size']),
            '--ckpt', str(ckpt_path), '--num-sampling-steps', str(BENCHMARK_CONFIG['num_sampling_steps']),
            '--outdir', str(PATHS['calibration_data']), '--filename', CALIB_DATA_NAME,
            '--cfg-scale', str(BENCHMARK_CONFIG['cfg_scale']), '--seed', str(BENCHMARK_CONFIG['seed']),
            '--vae-path', str(PATHS['vae_model'])],
            cwd=PATHS['ptq4dit_repo'], env_vars=env_vars
        )

    calib_qdit_path = PATHS['calibration_data'] / CALIB_DATA_QDIT_NAME
    if not calib_qdit_path.exists() and (BASE_DIR / 'convert_calib_data.py').exists():
        logger.info("Converting calibration data for Q-DiT...")
        run_script(['convert_calib_data.py', '--input', str(calib_path), '--output', str(calib_qdit_path)],
                   cwd=BASE_DIR, env_vars=env_vars)

    noise_file = PATHS['initial_noise'] / VIS_NOISE_NAME
    labels_file = PATHS['initial_noise'] / VIS_LABELS_NAME
    if not noise_file.exists() or not labels_file.exists():
        logger.info("Generating fixed initial noise and labels for visualization...")
        torch.manual_seed(BENCHMARK_CONFIG['seed'])
        n, latent_size = BENCHMARK_CONFIG['num_vis_samples'], BENCHMARK_CONFIG['image_size'] // 8
        torch.save(torch.randn(n, 4, latent_size, latent_size), noise_file)
        torch.save(torch.tensor(BENCHMARK_CONFIG['vis_class_labels'], dtype=torch.long), labels_file)
    
    ref_path = PATHS['reference_batch'] / REF_BATCH_NAME
    if not ref_path.exists():
        logger.info("Downloading FID reference batch...")
        subprocess.run(['wget', '-q', '-O', str(ref_path), "https://openaipublic.blob.core.windows.net/diffusion/jul-2021/ref_batches/imagenet/256/VIRTUAL_imagenet256_labeled.npz"], check=True)

    # 生成未量化模型的参考图片
    generate_reference_images(gpu_id)

# --- 6. 基准测试函数 ---
def worker_task(task_config):
    method_name, gpu_id, func = task_config['method'], task_config['gpu_id'], task_config['func']
    process_name = f"{method_name}-GPU{gpu_id}"
    setup_logging(process_name, log_file_mode='w')
    logger = logging.getLogger()
    logger.info(f"Starting task on GPU {gpu_id}")
    env_vars = {"CUDA_VISIBLE_DEVICES": str(gpu_id)}
    try:
        return method_name, func(env_vars)
    except Exception:
        logger.error(f"Benchmark for {method_name} failed.", exc_info=True)
        return method_name, None

# --- 各项目的具体实现 ---
def benchmark_ditas(env_vars):
    logger = logging.getLogger()
    w, a = BENCHMARK_CONFIG['target_w_bit'], BENCHMARK_CONFIG['target_a_bit']
    out_dir = RESULTS_DIR / f'ditas_w{w}a{a}'; out_dir.mkdir(parents=True, exist_ok=True)

    quant_ckpt = out_dir / f"W{w}A{a}_{BENCHMARK_CONFIG['num_sampling_steps']}_{BENCHMARK_CONFIG['image_size']}.pt"

    # 检查量化模型是否已存在，如果存在则跳过量化步骤
    if not quant_ckpt.exists():
        logger.info("Quantizing with DiTAS...")
        run_script([str(PATHS['ditas_repo'] / 'QuantDiT.py')] + [
            '--model', 'DiT-XL/2', '--image-size', str(BENCHMARK_CONFIG['image_size']),
            '--ckpt', str(PATHS['pretrained_models'] / PRETRAINED_CKPT_NAME),
            '--weight-bit', str(w), '--act-bit', str(a), '--num-sampling-steps', str(BENCHMARK_CONFIG['num_sampling_steps']),
            '--seed', str(BENCHMARK_CONFIG['seed']), '--vae-path', str(PATHS['vae_model']), '--results-dir', str(out_dir)
        ], cwd=PATHS['ditas_repo'], env_vars=env_vars)
    else:
        logger.info(f"DiTAS quantized model already exists at {quant_ckpt}, skipping quantization step.")

    # 检查FID样本是否已存在
    fid_npz_path = out_dir / 'samples.npz'
    if not fid_npz_path.exists():
        logger.info("Sampling for FID...")
        run_script([str(PATHS['ditas_repo'] / 'sample_merge_TAS.py')] + [
            '--model', 'DiT-XL/2', '--image-size', str(BENCHMARK_CONFIG['image_size']),
            '--path', str(quant_ckpt), '--num-fid-samples', str(BENCHMARK_CONFIG['num_fid_samples']),
            '--sample-dir', str(out_dir), '--global-seed', str(BENCHMARK_CONFIG['seed']),
            '--cfg-scale', str(BENCHMARK_CONFIG['cfg_scale']), '--vae-path', str(PATHS['vae_model'])
        ], cwd=PATHS['ditas_repo'], env_vars=env_vars, is_torchrun=True)

        # 重命名生成的NPZ文件
        # DiTAS可能将NPZ文件保存在out_dir或其父目录中
        npz_files = list(out_dir.rglob('*.npz')) + list(out_dir.parent.glob(f'{out_dir.name}*.npz'))
        for npz_file in npz_files:
            if 'samples' not in npz_file.name:
                target_path = out_dir / 'samples.npz'
                npz_file.rename(target_path)
                fid_npz_path = target_path
                break
    else:
        logger.info(f"DiTAS FID samples already exist at {fid_npz_path}, skipping FID sampling step.")

    # 检查可视化图像是否已存在
    vis_dir = out_dir / "visuals"
    if not vis_dir.exists() or len(list(vis_dir.glob('*.png'))) < BENCHMARK_CONFIG['num_vis_samples']:
        logger.info("Sampling for visualization...")
        run_script([str(PATHS['ditas_repo'] / 'sample_merge_TAS.py')] + [
            '--model', 'DiT-XL/2', '--image-size', str(BENCHMARK_CONFIG['image_size']),
            '--path', str(quant_ckpt), '--sample-dir', str(out_dir), '--global-seed', str(BENCHMARK_CONFIG['seed']),
            '--cfg-scale', str(BENCHMARK_CONFIG['cfg_scale']), '--vae-path', str(PATHS['vae_model']),
            '--visualize', '--noise-path', str(PATHS['initial_noise'] / VIS_NOISE_NAME),
            '--labels-path', str(PATHS['initial_noise'] / VIS_LABELS_NAME)
        ], cwd=PATHS['ditas_repo'], env_vars=env_vars, is_torchrun=True)
    else:
        logger.info(f"DiTAS visualization images already exist in {vis_dir}, skipping visualization step.")
    
    return {'fid_npz': fid_npz_path, 'vis_images': sorted(list(vis_dir.glob('*.png')))}

def benchmark_ptq4dit(env_vars):
    logger = logging.getLogger()
    w, a = BENCHMARK_CONFIG['target_w_bit'], BENCHMARK_CONFIG['target_a_bit']
    # PTQ4DiT 的结果会保存在一个带时间戳的子目录中，我们需要先找到它。
    # 为了简化，我们直接在主结果目录操作
    out_dir = RESULTS_DIR / f'ptq4dit_w{w}a{a}'; out_dir.mkdir(parents=True, exist_ok=True)
    
    quant_ckpt = out_dir / "qnn_model.pth"
    
    # ==================== 修改点 1：检查量化模型是否已存在 ====================
    if not quant_ckpt.exists():
        logger.info("Quantizing with PTQ4DiT...")
        # 注意：PTQ4DiT 会在 outdir 下创建带时间戳的子目录，这会使路径混乱
        # 我们修改 quant_sample.py，让它直接在 --outdir 保存 ckpt.pth
        # 假设 quant_sample.py 已被修改，或者我们直接在这里处理
        run_script([str(PATHS['ptq4dit_repo'] / 'quant_sample.py')] + [
            '--model', 'DiT-XL/2', '--image-size', str(BENCHMARK_CONFIG['image_size']),
            '--ckpt', str(PATHS['pretrained_models'] / PRETRAINED_CKPT_NAME), '--num-sampling-steps', str(BENCHMARK_CONFIG['num_sampling_steps']),
            '--weight_bit', str(w), '--act_bit', str(a), '--sm_abit', '8',
            '--cali_data_path', str(PATHS['calibration_data'] / CALIB_DATA_NAME),
            '--cali_n', str(BENCHMARK_CONFIG['ptq4dit_cali_n']),
            '--cali_batch_size', str(BENCHMARK_CONFIG['ptq4dit_cali_batch_size']),
            '--cali_st', str(BENCHMARK_CONFIG['ptq4dit_cali_st']),
            '--cali_iters', str(BENCHMARK_CONFIG['ptq4dit_recon_iters']),
            '--outdir', str(out_dir), '--ptq', '--recon', '--seed', str(BENCHMARK_CONFIG['seed']), '--vae-path', str(PATHS['vae_model'])
        ], cwd=PATHS['ptq4dit_repo'], env_vars=env_vars)

        # PTQ4DiT 会在 outdir 下创建时间戳子目录，我们需要从中移动量化模型文件
        # 这是一个不好的设计，我们在这里修复它
        # 查找最新的时间戳子目录
        subdirs = [d for d in out_dir.iterdir() if d.is_dir()]
        if subdirs:
            latest_subdir = max(subdirs, key=os.path.getmtime)
            # PTQ4DiT 实际保存的文件名是 qnn_model.pth，不是 ckpt.pth
            if (latest_subdir / "qnn_model.pth").exists():
                (latest_subdir / "qnn_model.pth").rename(quant_ckpt)
            elif (latest_subdir / "ckpt.pth").exists():
                (latest_subdir / "ckpt.pth").rename(quant_ckpt)
    else:
        logger.info(f"PTQ4DiT quantized model already exists at {quant_ckpt}, skipping quantization step.")

    if not quant_ckpt.exists(): 
        logger.error("PTQ4DiT quantization failed: ckpt.pth not found after execution.")
        return None

    # ==================== 修改点 2：拆分采样逻辑 ====================
    # --- FID 采样 ---
    fid_npz_path = out_dir / "samples.npz"
    if not fid_npz_path.exists():
        logger.info("Sampling for FID with PTQ4DiT...")
        run_script([str(PATHS['ptq4dit_repo'] / 'quant_sample.py')] + [
            '--model', 'DiT-XL/2', '--image-size', str(BENCHMARK_CONFIG['image_size']),
            '--ckpt', str(PATHS['pretrained_models'] / PRETRAINED_CKPT_NAME), '--num-sampling-steps', str(BENCHMARK_CONFIG['num_sampling_steps']),
            '--weight_bit', str(w), '--act_bit', str(a), '--sm_abit', '16',  # 提高注意力权重精度
            '--recon',  # 启用重构训练以提高量化质量
            '--outdir', str(out_dir), '--inference',  # 移除 --ptq 参数
            '--n_c', str(max(1, BENCHMARK_CONFIG['num_fid_samples'] // 1000)), '--c_begin', '0', '--c_end', '999',
            '--resume', '--cali_ckpt', str(quant_ckpt),
            '--cfg-scale', str(BENCHMARK_CONFIG['cfg_scale']), '--seed', str(BENCHMARK_CONFIG['seed']),
            '--vae-path', str(PATHS['vae_model'])
            # 移除 --cali_data_path，推理模式不需要校准数据
        ], cwd=PATHS['ptq4dit_repo'], env_vars=env_vars)

        if not convert_pngs_to_npz(out_dir / "inference", fid_npz_path, BENCHMARK_CONFIG['num_fid_samples']):
            fid_npz_path = None
    else:
        logger.info(f"PTQ4DiT FID samples already exist at {fid_npz_path}, skipping FID sampling step.")

    # --- 可视化采样 ---
    vis_dir = out_dir / "visuals"
    if not vis_dir.exists() or len(list(vis_dir.glob('*.png'))) < BENCHMARK_CONFIG['num_vis_samples']:
        logger.info("Sampling for visualization with PTQ4DiT...")
        run_script([str(PATHS['ptq4dit_repo'] / 'quant_sample.py')] + [
            '--model', 'DiT-XL/2', '--image-size', str(BENCHMARK_CONFIG['image_size']),
            '--ckpt', str(PATHS['pretrained_models'] / PRETRAINED_CKPT_NAME), '--num-sampling-steps', str(BENCHMARK_CONFIG['num_sampling_steps']),
            '--weight_bit', str(w), '--act_bit', str(a), '--sm_abit', '16',  # 提高注意力权重精度
            '--recon',  # 启用重构训练以提高量化质量
            '--outdir', str(out_dir), '--resume', '--cali_ckpt', str(quant_ckpt),  # 移除 --ptq 参数
            '--cfg-scale', str(BENCHMARK_CONFIG['cfg_scale']), '--seed', str(BENCHMARK_CONFIG['seed']),
            '--vae-path', str(PATHS['vae_model']),
            '--visualize', '--noise-path', str(PATHS['initial_noise'] / VIS_NOISE_NAME),
            '--labels-path', str(PATHS['initial_noise'] / VIS_LABELS_NAME)
            # 移除 --cali_data_path，推理模式不需要校准数据
        ], cwd=PATHS['ptq4dit_repo'], env_vars=env_vars)
    else:
        logger.info(f"PTQ4DiT visualization images already exist in {vis_dir}, skipping visualization step.")
    
    return {'fid_npz': fid_npz_path, 'vis_images': sorted(list(vis_dir.glob('*.png')))}

def benchmark_q_dit(env_vars):
    logger = logging.getLogger()
    w, a = BENCHMARK_CONFIG['target_w_bit'], BENCHMARK_CONFIG['target_a_bit']
    out_dir = RESULTS_DIR / f'qdit_w{w}a{a}'; out_dir.mkdir(parents=True, exist_ok=True)
    calib_path = PATHS['calibration_data'] / CALIB_DATA_QDIT_NAME

    # 检查FID样本是否已存在
    fid_npz_path = out_dir / 'samples.npz'
    if not fid_npz_path.exists():
        logger.info("Quantizing with Q-DiT and sampling for FID...")
        run_script([str(PATHS['q_dit_repo'] / 'scripts' / 'quant_main.py')] + [
            '--model', 'DiT-XL/2', '--image-size', str(BENCHMARK_CONFIG['image_size']),
            '--ckpt', str(PATHS['pretrained_models'] / PRETRAINED_CKPT_NAME),
            '--wbits', str(w), '--abits', str(a), '--use_gptq',
            '--weight_group_size', '[128]*112', '--act_group_size', '[128]*112',
            '--calib_data_path', str(calib_path),
            '--num-fid-samples', str(BENCHMARK_CONFIG['num_fid_samples']),
            '--num-sampling-steps', str(BENCHMARK_CONFIG['num_sampling_steps']),
            '--cfg-scale', str(BENCHMARK_CONFIG['cfg_scale']), '--seed', str(BENCHMARK_CONFIG['seed']),
            '--results-dir', str(out_dir), '--vae-path', str(PATHS['vae_model'])
        ], cwd=PATHS['q_dit_repo'], env_vars=env_vars)

        # 重命名生成的NPZ文件
        for npz_file in out_dir.rglob('*.npz'):
            if 'samples.npz' not in npz_file.name:
                npz_file.rename(out_dir / 'samples.npz'); fid_npz_path = out_dir / 'samples.npz'
    else:
        logger.info(f"Q-DiT FID samples already exist at {fid_npz_path}, skipping FID sampling step.")

    # 检查可视化图像是否已存在
    vis_dir = out_dir / 'visuals'
    if not vis_dir.exists() or len(list(vis_dir.glob('*.png'))) < BENCHMARK_CONFIG['num_vis_samples']:
        logger.info("Re-quantizing with Q-DiT and sampling for visualization...")
        run_script([str(PATHS['q_dit_repo'] / 'scripts' / 'quant_main.py')] + [
            '--model', 'DiT-XL/2', '--image-size', str(BENCHMARK_CONFIG['image_size']),
            '--ckpt', str(PATHS['pretrained_models'] / PRETRAINED_CKPT_NAME),
            '--wbits', str(w), '--abits', str(a), '--use_gptq',
            '--weight_group_size', '[128]*112', '--act_group_size', '[128]*112',
            '--calib_data_path', str(calib_path),
            '--num-sampling-steps', str(BENCHMARK_CONFIG['num_sampling_steps']),
            '--cfg-scale', str(BENCHMARK_CONFIG['cfg_scale']), '--seed', str(BENCHMARK_CONFIG['seed']),
            '--results-dir', str(out_dir), '--vae-path', str(PATHS['vae_model']),
            '--visualize', '--noise-path', str(PATHS['initial_noise'] / VIS_NOISE_NAME),
            '--labels-path', str(PATHS['initial_noise'] / VIS_LABELS_NAME)
        ], cwd=PATHS['q_dit_repo'], env_vars=env_vars)
    else:
        logger.info(f"Q-DiT visualization images already exist in {vis_dir}, skipping visualization step.")
    
    return {'fid_npz': fid_npz_path, 'vis_images': sorted(list((out_dir / 'visuals').glob('*.png')))}

def benchmark_tq_dit(env_vars):
    logger = logging.getLogger()
    w, a = BENCHMARK_CONFIG['target_w_bit'], BENCHMARK_CONFIG['target_a_bit']
    out_dir = RESULTS_DIR / f'tqdit_w{w}a{a}'; out_dir.mkdir(parents=True, exist_ok=True)

    # ==================== 终极修复：使用 python -m 调用 ====================
    # 我们将把工作目录 cwd 设置为 TQ-DiT 的根目录
    project_cwd = PATHS['tq_dit_repo']
    # 我们将脚本作为模块来运行：example.run_tqdit
    # 这会强制 Python 将 cwd (即 TQ-DiT 根目录) 作为顶级包
    script_module = 'example.run_tqdit'

    # 构造命令列表，第一个元素是模块名，其他是参数
    base_args = [
        '--model', 'DiT-XL/2', '--image-size', str(BENCHMARK_CONFIG['image_size']),
        '--ckpt', str(PATHS['pretrained_models'] / PRETRAINED_CKPT_NAME),
        '--dataset_path', str(PATHS['calibration_data'] / CALIB_DATA_NAME),
        '--output_dir', str(out_dir),
        '--wbit', str(w), '--abit', str(a),
        '--num-sampling-steps', str(BENCHMARK_CONFIG['num_sampling_steps']),
        '--cfg-scale', str(BENCHMARK_CONFIG['cfg_scale']), '--seed', str(BENCHMARK_CONFIG['seed']),
        '--vae-path', str(PATHS['vae_model'])
    ]

    # 检查FID样本是否已存在
    fid_npz_path = out_dir / 'samples.npz'
    if not fid_npz_path.exists():
        logger.info("Quantizing with TQ-DiT and sampling for FID...")
        fid_args = base_args + [
            '--output_npz', str(fid_npz_path),
            '--num_fid_samples', str(BENCHMARK_CONFIG['num_fid_samples']),
        ]
        # run_script 的 command_list 现在是 ['example.run_tqdit', '--arg1', 'val1', ...]
        run_script([script_module] + fid_args, cwd=project_cwd, env_vars=env_vars, use_module_flag=True)
    else:
        logger.info(f"TQ-DiT FID samples already exist at {fid_npz_path}, skipping FID sampling step.")

    # 检查可视化图像是否已存在
    vis_dir = out_dir / 'visuals'
    if not vis_dir.exists() or len(list(vis_dir.glob('*.png'))) < BENCHMARK_CONFIG['num_vis_samples']:
        logger.info("Re-quantizing with TQ-DiT and sampling for visualization...")
        vis_args = base_args + [
            '--visualize',
            '--noise-path', str(PATHS['initial_noise'] / VIS_NOISE_NAME),
            '--labels-path', str(PATHS['initial_noise'] / VIS_LABELS_NAME)
        ]
        run_script([script_module] + vis_args, cwd=project_cwd, env_vars=env_vars, use_module_flag=True)
    else:
        logger.info(f"TQ-DiT visualization images already exist in {vis_dir}, skipping visualization step.")
    # =====================================================================

    return {'fid_npz': fid_npz_path, 'vis_images': sorted(list((out_dir / 'visuals').glob('*.png')))}

def benchmark_qua2sedimo(env_vars):
    logger = logging.getLogger()
    w, a = BENCHMARK_CONFIG['target_w_bit'], BENCHMARK_CONFIG['target_a_bit']
    out_dir = RESULTS_DIR / f'qua2sedimo_w{w}a{a}'; out_dir.mkdir(parents=True, exist_ok=True)

    # 设置OpenBLAS环境变量以避免内存分配问题
    env_vars = env_vars.copy()
    env_vars.update({
        'OPENBLAS_NUM_THREADS': '1',
        'MKL_NUM_THREADS': '1',
        'OMP_NUM_THREADS': '1'
    })

    # Qua2SeDiMo 需要预先生成的 multiquantizer checkpoint
    multiquantizer_dir = PATHS['qua2sedimo_repo'] / 'multiquantizers'
    multiquantizer_dir.mkdir(exist_ok=True)
    multiquantizer_ckpt = multiquantizer_dir / 'dit_mq.pt'

    # 检查是否需要生成 multiquantizer checkpoint
    if not multiquantizer_ckpt.exists():
        logger.info("Generating Qua2SeDiMo multiquantizer checkpoint...")
        logger.warning("This may take a long time and require significant VRAM (24-32GB)")

        # 使用我们的自定义multiquantizer生成脚本
        run_script([str(BASE_DIR / 'create_qua2sedimo_multiquantizer.py')] + [
            '--weight_bit', str(w),
            '--seed', str(BENCHMARK_CONFIG['seed']),
            '--dit_ckpt', str(PATHS['pretrained_models'] / PRETRAINED_CKPT_NAME),
            '--image_size', str(BENCHMARK_CONFIG['image_size']),
            '--output_dir', str(multiquantizer_dir)
        ], cwd=BASE_DIR, env_vars=env_vars)

        if not multiquantizer_ckpt.exists():
            logger.error("Failed to generate Qua2SeDiMo multiquantizer checkpoint")
            return None
    else:
        logger.info(f"Qua2SeDiMo multiquantizer checkpoint already exists at {multiquantizer_ckpt}, skipping generation step.")

    # 创建量化配置文件目录
    quant_config_dir = PATHS['qua2sedimo_repo'] / 'quant_configs'
    quant_config_dir.mkdir(exist_ok=True)

    # 使用默认的量化配置（如果存在）
    quant_config_file = quant_config_dir / f'dit_{w}0.pkl'  # 原始项目使用这种命名

    # 检查FID样本是否已存在
    fid_npz_path = out_dir / "samples.npz"
    fid_sample_dir = out_dir / 'fid_samples'

    if not fid_npz_path.exists():
        logger.info("Sampling for FID with Qua2SeDiMo...")
        # 创建自定义的推理脚本来支持我们的基准测试需求
        custom_infer_script = create_qua2sedimo_inference_script(out_dir)

        # FID 采样
        run_script([str(custom_infer_script)] + [
            '--outdir', str(fid_sample_dir),
            '--weight_bit', str(w),
            '--multi_chkpt', str(multiquantizer_ckpt),
            '--seed', str(BENCHMARK_CONFIG['seed']),
            '--n_imgs', str(BENCHMARK_CONFIG['num_fid_samples']),
            '--res', str(BENCHMARK_CONFIG['image_size']),
            '--num_inference_steps', str(BENCHMARK_CONFIG['num_sampling_steps']),
            '--dit_ckpt', str(PATHS['pretrained_models'] / PRETRAINED_CKPT_NAME),
            '--vae_path', str(PATHS['vae_model'])
        ] + (['--q_config', str(quant_config_file)] if quant_config_file.exists() else []),
        cwd=PATHS['qua2sedimo_repo'], env_vars=env_vars)

        # 转换 PNG 到 NPZ
        if not convert_pngs_to_npz(fid_sample_dir, fid_npz_path, BENCHMARK_CONFIG['num_fid_samples']):
            fid_npz_path = None
    else:
        logger.info(f"Qua2SeDiMo FID samples already exist at {fid_npz_path}, skipping FID sampling step.")

    # 检查可视化图像是否已存在
    vis_dir = out_dir / "visuals"
    if not vis_dir.exists() or len(list(vis_dir.glob('*.png'))) < BENCHMARK_CONFIG['num_vis_samples']:
        logger.info("Sampling for visualization with Qua2SeDiMo...")
        # 如果还没有创建推理脚本，则创建
        if 'custom_infer_script' not in locals():
            custom_infer_script = create_qua2sedimo_inference_script(out_dir)

        # 可视化采样
        run_script([str(custom_infer_script)] + [
            '--outdir', str(vis_dir),
            '--weight_bit', str(w),
            '--multi_chkpt', str(multiquantizer_ckpt),
            '--seed', str(BENCHMARK_CONFIG['seed']),
            '--n_imgs', str(BENCHMARK_CONFIG['num_vis_samples']),
            '--res', str(BENCHMARK_CONFIG['image_size']),
            '--num_inference_steps', str(BENCHMARK_CONFIG['num_sampling_steps']),
            '--visualize',
            '--noise-path', str(PATHS['initial_noise'] / VIS_NOISE_NAME),
            '--labels-path', str(PATHS['initial_noise'] / VIS_LABELS_NAME),
            '--dit_ckpt', str(PATHS['pretrained_models'] / PRETRAINED_CKPT_NAME),
            '--vae_path', str(PATHS['vae_model'])
        ] + (['--q_config', str(quant_config_file)] if quant_config_file.exists() else []),
        cwd=PATHS['qua2sedimo_repo'], env_vars=env_vars)
    else:
        logger.info(f"Qua2SeDiMo visualization images already exist in {vis_dir}, skipping visualization step.")

    return {'fid_npz': fid_npz_path, 'vis_images': sorted(list(vis_dir.glob('*.png')))}

def create_qua2sedimo_inference_script(output_dir):
    """创建适配我们基准测试的 Qua2SeDiMo 推理脚本"""
    script_content = '''
import argparse, os, datetime, yaml
import logging
from pytorch_lightning import seed_everything
import torch
import numpy as np
from PIL import Image
from tqdm import tqdm
import sys
sys.path.append('.')

from qdiff import QuantModelMultiQ

def load_dit_model_from_checkpoint(ckpt_path, image_size=256):
    """从本地checkpoint加载DiT模型 - 使用与其他算法相同的模式"""
    import sys
    from pathlib import Path

    # 添加DiT相关路径 - 需要找到项目根目录
    current_dir = Path(__file__).parent
    # 从当前脚本位置向上查找项目根目录（包含github-repos的目录）
    project_root = current_dir
    while project_root.parent != project_root:  # 避免无限循环
        if (project_root / "github-repos").exists():
            break
        project_root = project_root.parent

    # 优先使用PTQ4DiT的模块，因为它们最稳定
    dit_paths = [
        project_root / "github-repos" / "PTQ4DiT",
        project_root / "github-repos" / "Q-DiT",
        project_root / "github-repos" / "DiTAS"
    ]

    for dit_path in dit_paths:
        if dit_path.exists():
            sys.path.insert(0, str(dit_path))
            break

    try:
        # 使用与其他算法相同的导入方式
        from diffusion import create_diffusion
        from models import DiT_models
        from download import find_model
    except ImportError as e:
        raise ImportError(f"Cannot import DiT modules: {e}")

    # 使用与其他算法相同的模型加载方式
    latent_size = image_size // 8
    model = DiT_models["DiT-XL/2"](
        input_size=latent_size,
        num_classes=1000
    )

    # 使用find_model函数加载权重
    state_dict = find_model(ckpt_path)
    model.load_state_dict(state_dict)
    model.eval()

    # 创建扩散过程
    diffusion = create_diffusion(timestep_respacing="")

    return model, diffusion

def main():
    parser = argparse.ArgumentParser()
    parser.add_argument("--outdir", type=str, required=True, help="dir to write results to")
    parser.add_argument("--res", type=int, default=256, help="image height, in pixel space")
    parser.add_argument("--seed", type=int, default=42, help="the seed (for reproducible sampling)")
    parser.add_argument("--weight_bit", type=int, default=4, help="int bit for weight quantization")
    parser.add_argument("--multi_chkpt", type=str, required=True, help="path to multiquantizer checkpoint")
    parser.add_argument("--q_config", type=str, default=None, help="path to quantization config")
    parser.add_argument("--n_imgs", type=int, default=1, help="number of images to generate")
    parser.add_argument("--visualize", action="store_true", help="use fixed noise for visualization")
    parser.add_argument("--noise-path", type=str, help="path to fixed noise file")
    parser.add_argument("--labels-path", type=str, help="path to fixed labels file")
    parser.add_argument("--num_inference_steps", type=int, default=250, help="number of inference steps")
    parser.add_argument("--dit_ckpt", type=str, default="models/DiT-XL-2-256x256.pt", help="path to DiT checkpoint")
    parser.add_argument("--vae_path", type=str, default="models/local_vae_model", help="path to VAE model")

    opt = parser.parse_args()

    seed_everything(opt.seed)
    os.makedirs(opt.outdir, exist_ok=True)

    # 设置日志
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    logger = logging.getLogger(__name__)

    # 方法1: 尝试使用本地DiT模型和VAE
    try:
        logger.info("Attempting to load local DiT model and VAE...")

        # 加载本地DiT模型
        dit_model, diffusion = load_dit_model_from_checkpoint(opt.dit_ckpt, opt.res)
        dit_model = dit_model.to("cuda").eval()

        # 加载本地VAE
        from diffusers import AutoencoderKL
        vae = AutoencoderKL.from_pretrained(opt.vae_path, local_files_only=True)
        vae = vae.to("cuda").eval()

        logger.info("Successfully loaded local models")
        use_local_models = True

    except Exception as e:
        logger.error(f"Failed to load local models: {e}")
        logger.error("Cannot proceed without local models in offline mode")
        raise RuntimeError(f"Failed to load required local models: {e}")

    # 设置量化参数 - 与原始脚本保持一致
    wq_params = {'n_bits': opt.weight_bit, 'channel_wise': True, 'scale_method': 'mse'}
    aq_params = {'n_bits': 8, 'channel_wise': False, 'scale_method': 'mse', 'leaf_param': False, 'online_act_quant': True}

    # 使用本地模型的量化流程
    logger.info("Setting up quantization for local DiT model...")

    # 创建量化模型
    qnn = QuantModelMultiQ(
        model=dit_model, weight_quant_params=wq_params, act_quant_params=aq_params,
        act_quant_mode="qdiff")
    qnn.to("cuda")
    qnn.eval()

    # 初始化量化状态
    qnn.set_quant_state(weight_quant=False, act_quant=False)

    # 加载 multiquantizer checkpoint
    if opt.multi_chkpt and os.path.exists(opt.multi_chkpt):
        logger.info(f"Loading multiquantizer checkpoint from {opt.multi_chkpt}")
        qnn.load_mq_state_dicts(filepath=opt.multi_chkpt)

    qnn.set_quant_state(weight_quant=True, act_quant=False)

    # 加载量化配置
    if opt.q_config and os.path.exists(opt.q_config):
        logger.info(f"Loading quantization config from {opt.q_config}")
        qnn.load_quant_config(opt.q_config)

    # 创建一个包装器来正确处理DiT模型的参数
    class DiTQuantWrapper:
        def __init__(self, qnn_model):
            self.qnn = qnn_model

        def __call__(self, x, t, y):
            # QuantModelMultiQ的forward方法签名是(x, timesteps=None, context=None)
            # 但DiT需要(x, t, y)，所以我们需要适配
            return self.qnn.model(x, t, y)

    # 使用包装器
    wrapped_qnn = DiTQuantWrapper(qnn)

    # 生成图像
    if opt.visualize and opt.noise_path and opt.labels_path:
        # 使用固定的噪声和标签进行可视化
        logger.info("Using fixed noise and labels for visualization")
        noise = torch.load(opt.noise_path)
        labels = torch.load(opt.labels_path)

        # 确保数量匹配
        n_samples = min(opt.n_imgs, len(noise), len(labels))
        noise = noise[:n_samples].to("cuda")
        labels = labels[:n_samples].to("cuda")

        # 调整噪声尺寸以匹配目标分辨率
        if opt.res == 256 and noise.shape[-1] != 32:  # 256/8 = 32
            # 如果噪声尺寸不匹配，进行插值调整
            target_size = opt.res // 8
            noise = torch.nn.functional.interpolate(noise, size=(target_size, target_size), mode='bilinear', align_corners=False)

        # 使用本地模型生成图像
        logger.info("Generating images with local DiT model...")
        images = []

        for i in range(n_samples):
            # 单个样本的噪声和标签
            z = noise[i:i+1]  # [1, 4, 32, 32]
            y = labels[i:i+1]  # [1]

            # 使用DiT模型和扩散过程生成图像
            with torch.no_grad():
                # 扩散采样
                samples = diffusion.p_sample_loop(
                    wrapped_qnn, z.shape, z, clip_denoised=False, model_kwargs={"y": y}
                )

                # VAE解码
                samples = vae.decode(samples / 0.18215).sample
                samples = (samples / 2 + 0.5).clamp(0, 1)

                # 转换为PIL图像
                sample = samples[0].cpu().permute(1, 2, 0).numpy()
                sample = (sample * 255).astype(np.uint8)
                images.append(Image.fromarray(sample))
    else:
        # 随机生成 - 与原始脚本保持一致
        logger.info(f"Generating {opt.n_imgs} random images")
        torch.manual_seed(opt.seed)  # 使用指定的种子

        # 生成类别标签
        if opt.n_imgs == 1:
            cls_ids = [833]  # 使用原始脚本的默认类别
        else:
            # 为多个图像生成随机类别
            cls_ids = torch.randint(0, 1000, (opt.n_imgs,)).tolist()

        # 使用本地模型生成随机图像
        logger.info("Generating random images with local DiT model...")
        images = []

        for i in range(opt.n_imgs):
            # 生成随机噪声
            latent_size = opt.res // 8
            z = torch.randn(1, 4, latent_size, latent_size, device="cuda")
            y = torch.tensor([cls_ids[i]], device="cuda")

            with torch.no_grad():
                # 扩散采样
                samples = diffusion.p_sample_loop(
                    wrapped_qnn, z.shape, z, clip_denoised=False, model_kwargs={"y": y}
                )

                # VAE解码
                samples = vae.decode(samples / 0.18215).sample
                samples = (samples / 2 + 0.5).clamp(0, 1)

                # 转换为PIL图像
                sample = samples[0].cpu().permute(1, 2, 0).numpy()
                sample = (sample * 255).astype(np.uint8)
                images.append(Image.fromarray(sample))

    # 保存图像
    for i, img in enumerate(images):
        img_path = os.path.join(opt.outdir, f"sample_{i:05d}.png")

        # 如果需要调整图像尺寸到目标分辨率
        if img.size != (opt.res, opt.res):
            img = img.resize((opt.res, opt.res), Image.Resampling.LANCZOS)

        img.save(img_path)
        logger.info(f"Saved image {i+1}/{len(images)} to {img_path}")

    logger.info(f"Generated {len(images)} images in {opt.outdir}")

if __name__ == "__main__":
    main()
'''

    script_path = output_dir / "qua2sedimo_inference.py"
    with open(script_path, 'w') as f:
        f.write(script_content)

    return script_path

# --- 7. 评估和总结 ---
def evaluate_and_summarize(results, gpu_id):
    logger = logging.getLogger()
    logger.info("--- Phase 3: Evaluating FID and Stitching Images ---")
    
    fid_results = {method: paths['fid_npz'] for method, paths in results.items() if paths and paths.get('fid_npz')}
    evaluator_script = BASE_DIR / 'pytorch_fid_evaluator.py'
    summary = {}
    if fid_results:
        for method, sample_npz_path in fid_results.items():
            if not (sample_npz_path and Path(sample_npz_path).exists()):
                logger.warning(f"Sample NPZ not found for {method}. Skipping evaluation.")
                continue
            logger.info(f"--- Evaluating {method} ---")
            try:
                output = run_script([str(evaluator_script), str(PATHS['reference_batch'] / REF_BATCH_NAME), str(sample_npz_path)],
                                    cwd=BASE_DIR, env_vars={"CUDA_VISIBLE_DEVICES": str(gpu_id)})
                metrics = {}
                patterns = {'FID': r"FID Score:\s*([\d\.]+)", 'Inception Score': r"Inception Score:\s*([\d\.]+)\s*±\s*([\d\.]+)"}
                for key, pattern in patterns.items():
                    match = re.search(pattern, output)
                    if match: metrics[key] = float(match.group(1))
                summary[method] = metrics
            except Exception:
                logger.error(f"Evaluation failed for {method}", exc_info=True)

    print("\n\n" + "="*80 + "\n" + " " * 25 + "Benchmark Summary" + "\n" + "="*80)
    header = f"{'Method':<20} | {'FID':<10} | {'sFID':<10} | {'Inception Score':<20} | {'Precision':<10} | {'Recall':<10}"
    print(header + "\n" + "-" * len(header))
    sorted_methods = sorted(summary.keys())
    for method in sorted_methods:
        metrics = summary.get(method, {})
        fid, sfid, inception_score, precision, recall = (metrics.get(k, 'N/A') for k in ['FID', 'sFID', 'Inception Score', 'Precision', 'Recall'])
        print(f"{method:<20} | {fid if isinstance(fid, str) else f'{fid:<10.3f}'} | "
              f"{sfid if isinstance(sfid, str) else f'{sfid:<10.3f}'} | "
              f"{inception_score if isinstance(inception_score, str) else f'{inception_score:<20.3f}'} | "
              f"{precision if isinstance(precision, str) else f'{precision:<10.4f}'} | "
              f"{recall if isinstance(recall, str) else f'{recall:<10.4f}'}")
    print("="*80)

    vis_results = {method: paths['vis_images'] for method, paths in results.items() if paths and paths.get('vis_images')}
    if vis_results:
        logger.info("Creating multiple visual comparison groups with reference images...")

        methods = sorted(list(vis_results.keys()))
        if not (methods and vis_results.get(methods[0])):
            logger.warning(f"No visualization images found for {methods[0]}. Skipping stitch.")
            return

        # 添加参考图片到结果中
        ref_images_dir = COMMON_DATA_DIR / 'reference_images'
        if ref_images_dir.exists():
            ref_images = sorted(list(ref_images_dir.glob('*.png')))
            if ref_images:
                vis_results['Reference (FP32)'] = ref_images
                methods = ['Reference (FP32)'] + methods  # 将参考图片放在第一行

        # 创建三组对比图片，每组8张
        num_groups = 3
        images_per_group = 8

        for group_idx in range(num_groups):
            output_path = RESULTS_DIR / f'visual_comparison_group_{group_idx + 1}.png'
            logger.info(f"Creating visual comparison group {group_idx + 1} to {output_path}")

            # 计算当前组的图片索引范围
            start_idx = group_idx * images_per_group
            end_idx = min(start_idx + images_per_group, BENCHMARK_CONFIG['num_vis_samples'])

            if start_idx >= len(vis_results.get(methods[0], [])):
                logger.warning(f"Not enough images for group {group_idx + 1}. Skipping.")
                continue

            # 获取第一张图片的尺寸
            first_method_images = vis_results.get(methods[0], [])
            if not first_method_images:
                logger.warning(f"No images found for {methods[0]}. Skipping group {group_idx + 1}.")
                continue

            first_img = Image.open(first_method_images[start_idx])
            img_w, img_h = first_img.size

            # 计算布局尺寸
            actual_images_in_group = min(images_per_group, end_idx - start_idx)
            margin = 40  # 边距
            coord_space = 60  # 坐标标注空间

            # 总尺寸：坐标空间 + 图片区域 + 边距
            total_width = coord_space + img_w * actual_images_in_group + margin
            total_height = coord_space + img_h * len(methods) + margin

            grid_img = Image.new('RGB', (total_width, total_height), 'white')
            draw = ImageDraw.Draw(grid_img)

            try:
                font = ImageFont.truetype("DejaVuSans-Bold.ttf", size=16)
                coord_font = ImageFont.truetype("DejaVuSans.ttf", size=12)
            except IOError:
                font = ImageFont.load_default()
                coord_font = ImageFont.load_default()

            # 绘制列坐标（图片索引）
            for j in range(actual_images_in_group):
                img_idx = start_idx + j
                col_x = coord_space + j * img_w + img_w // 2
                draw.text((col_x - 10, 10), f"Img {img_idx + 1}", fill="blue", font=coord_font, anchor="mm")
                # 绘制列分隔线
                if j > 0:
                    line_x = coord_space + j * img_w
                    draw.line([(line_x, coord_space), (line_x, total_height - margin)], fill="lightgray", width=1)

            # 为每个方法创建一行
            for i, method in enumerate(methods):
                row_y = coord_space + i * img_h

                # 绘制行坐标（方法名称）
                draw.text((5, row_y + img_h // 2), f"Row {i + 1}", fill="blue", font=coord_font, anchor="lm")
                draw.text((coord_space - 5, row_y + 5), method.upper(), fill="red", font=font, anchor="ra")

                # 绘制行分隔线
                if i > 0:
                    draw.line([(coord_space, row_y), (total_width - margin, row_y)], fill="lightgray", width=1)

                img_paths = vis_results.get(method, [])

                # 在当前组的范围内放置图片
                for j in range(actual_images_in_group):
                    img_idx = start_idx + j
                    img_x = coord_space + j * img_w
                    img_y = row_y

                    if img_idx < len(img_paths) and Path(img_paths[img_idx]).exists():
                        img = Image.open(img_paths[img_idx])
                        # 确保图片尺寸一致
                        if img.size != (img_w, img_h):
                            img = img.resize((img_w, img_h), Image.Resampling.LANCZOS)
                        grid_img.paste(img, (img_x, img_y))

                        # 在图片上添加坐标标注
                        img_draw = ImageDraw.Draw(grid_img)
                        coord_text = f"({i+1},{j+1})"
                        img_draw.text((img_x + 5, img_y + 5), coord_text, fill="yellow", font=coord_font)
                    else:
                        # 如果图片不存在，创建一个占位符
                        placeholder = Image.new('RGB', (img_w, img_h), 'lightgray')
                        placeholder_draw = ImageDraw.Draw(placeholder)
                        placeholder_draw.text((img_w//2, img_h//2), "Missing", fill="black", font=font, anchor="mm")
                        grid_img.paste(placeholder, (img_x, img_y))

            # 添加标题
            title_text = f"DiT Quantization Comparison - Group {group_idx + 1} (Images {start_idx + 1}-{end_idx})"
            title_bbox = draw.textbbox((0, 0), title_text, font=font)
            title_width = title_bbox[2] - title_bbox[0]
            draw.text(((total_width - title_width) // 2, total_height - 25), title_text, fill="black", font=font)

            grid_img.save(output_path)
            logger.info(f"Visual comparison group {group_idx + 1} saved to {output_path}")

        # 创建一个总览图片，显示所有组的第一张图片
        create_overview_comparison(vis_results, methods, RESULTS_DIR, logger)

def create_overview_comparison(vis_results, methods, results_dir, logger):
    """创建总览对比图片，显示每组的代表性图片"""
    logger.info("Creating overview comparison image with coordinates...")

    try:
        output_path = results_dir / 'visual_comparison_overview.png'

        # 选择每组的代表性图片索引（第1张、第9张、第17张）
        representative_indices = [0, 8, 16]  # 对应三组的第一张图片

        if not vis_results or not methods:
            logger.warning("No visualization results available for overview.")
            return

        # 获取第一张图片的尺寸
        first_method = methods[0]
        if not vis_results.get(first_method) or not Path(vis_results[first_method][0]).exists():
            logger.warning("Cannot find first image for overview.")
            return

        first_img = Image.open(vis_results[first_method][0])
        img_w, img_h = first_img.size

        # 计算布局尺寸
        margin = 40
        coord_space = 80
        title_space = 60

        # 总尺寸：标题空间 + 坐标空间 + 图片区域 + 边距
        total_width = coord_space + img_w * 3 + margin
        total_height = title_space + coord_space + img_h * len(methods) + margin

        grid_img = Image.new('RGB', (total_width, total_height), 'white')
        draw = ImageDraw.Draw(grid_img)

        try:
            font = ImageFont.truetype("DejaVuSans-Bold.ttf", size=16)
            title_font = ImageFont.truetype("DejaVuSans-Bold.ttf", size=20)
            coord_font = ImageFont.truetype("DejaVuSans.ttf", size=12)
        except IOError:
            font = ImageFont.load_default()
            title_font = ImageFont.load_default()
            coord_font = ImageFont.load_default()

        # 添加主标题
        main_title = "DiT Quantization Methods Overview Comparison"
        title_bbox = draw.textbbox((0, 0), main_title, font=title_font)
        title_width = title_bbox[2] - title_bbox[0]
        draw.text(((total_width - title_width) // 2, 10), main_title, fill="black", font=title_font)

        # 添加列标题（组名）
        group_names = ['Group 1 (Images 1-8)', 'Group 2 (Images 9-16)', 'Group 3 (Images 17-24)']
        for col, group_name in enumerate(group_names):
            col_x = coord_space + col * img_w + img_w // 2
            draw.text((col_x, title_space + 10), f"Col {col + 1}", fill="blue", font=coord_font, anchor="mm")
            draw.text((col_x, title_space + 25), group_name, fill="blue", font=coord_font, anchor="mm")

            # 绘制列分隔线
            if col > 0:
                line_x = coord_space + col * img_w
                draw.line([(line_x, title_space + coord_space), (line_x, total_height - margin)], fill="lightgray", width=1)

        # 为每个方法创建一行
        for row, method in enumerate(methods):
            row_y = title_space + coord_space + row * img_h

            # 绘制行坐标和方法标签
            draw.text((5, row_y + img_h // 2), f"Row {row + 1}", fill="blue", font=coord_font, anchor="lm")
            draw.text((coord_space - 5, row_y + 5), method.upper(), fill="red", font=font, anchor="ra")

            # 绘制行分隔线
            if row > 0:
                draw.line([(coord_space, row_y), (total_width - margin, row_y)], fill="lightgray", width=1)

            img_paths = vis_results.get(method, [])

            # 为每组选择代表性图片
            for col, img_idx in enumerate(representative_indices):
                img_x = coord_space + col * img_w
                img_y = row_y

                if img_idx < len(img_paths) and Path(img_paths[img_idx]).exists():
                    img = Image.open(img_paths[img_idx])
                    # 确保图片尺寸一致
                    if img.size != (img_w, img_h):
                        img = img.resize((img_w, img_h), Image.Resampling.LANCZOS)
                    grid_img.paste(img, (img_x, img_y))

                    # 在图片上添加坐标标注
                    coord_text = f"({row+1},{col+1})"
                    draw.text((img_x + 5, img_y + 5), coord_text, fill="yellow", font=coord_font)
                else:
                    # 创建占位符
                    placeholder = Image.new('RGB', (img_w, img_h), 'lightgray')
                    placeholder_draw = ImageDraw.Draw(placeholder)
                    placeholder_draw.text((img_w//2, img_h//2), f"Group {col+1}\nMissing",
                                        fill="black", font=font, anchor="mm")
                    grid_img.paste(placeholder, (img_x, img_y))

        # 添加底部说明
        footer_text = "Coordinates: (Row, Column) - Row: Method, Column: Image Group"
        draw.text((10, total_height - 25), footer_text, fill="gray", font=coord_font)

        grid_img.save(output_path)
        logger.info(f"Overview comparison image saved to {output_path}")

    except Exception as e:
        logger.error(f"Failed to create overview comparison: {e}")

# --- 8. 主函数 ---
def main():
    parser = argparse.ArgumentParser(description="Multi-GPU DiT Quantization Benchmark")
    parser.add_argument('--multi-gpu', action='store_true', help='Enable multi-GPU parallel execution.')
    parser.add_argument('--gpu-ids', type=int, nargs='+', default=[0], help='List of GPU IDs to use.')
    parser.add_argument('--tasks', type=str, nargs='+', default=['all'], help="List of tasks to run, e.g., 'ditas' 'ptq4dit'. 'all' runs all tasks.")
    args = parser.parse_args()

    setup_logging("Main", log_file_mode='w')
    main_logger = logging.getLogger()

    try:
        prepare_environment(args.gpu_ids[0])
    except Exception:
        main_logger.error("Failed to prepare environment. Exiting.", exc_info=True)
        return

    all_tasks = {
        'ditas': {'method': 'ditas', 'func': benchmark_ditas},
        'ptq4dit': {'method': 'ptq4dit', 'func': benchmark_ptq4dit},
        'q_dit': {'method': 'q_dit', 'func': benchmark_q_dit},
        'qua2sedimo': {'method': 'qua2sedimo', 'func': benchmark_qua2sedimo},
        # 'tq_dit': {'method': 'tq_dit', 'func': benchmark_tq_dit},
    }
    tasks_to_run = list(all_tasks.values()) if 'all' in args.tasks else [all_tasks[name] for name in args.tasks if name in all_tasks]

    if not tasks_to_run: main_logger.error("No valid tasks selected. Exiting."); return

    all_results = {}
    if args.multi_gpu and len(args.gpu_ids) > 1 and len(tasks_to_run) > 1:
        main_logger.info(f"Running in Multi-GPU mode on GPUs: {args.gpu_ids}")
        main_logger.info(f"Tasks to run: {[t['method'] for t in tasks_to_run]}")
        
        task_configs_for_pool = [{'method': task['method'], 'gpu_id': args.gpu_ids[i % len(args.gpu_ids)], 'func': task['func']} for i, task in enumerate(tasks_to_run)]
        
        with Pool(processes=len(args.gpu_ids)) as pool:
            results = pool.map(worker_task, task_configs_for_pool)
            for method, result_paths in results:
                if result_paths: all_results[method] = result_paths
    else:
        main_logger.info("Running in Single-GPU mode.")
        gpu_id = args.gpu_ids[0]
        for task in tasks_to_run:
            task_name = f"{task['method']}-GPU{gpu_id}"
            setup_logging(task_name, log_file_mode='w')
            logger = logging.getLogger()
            logger.info(f"--- Starting benchmark for {task['method']} on GPU {gpu_id} ---")
            env_vars = {"CUDA_VISIBLE_DEVICES": str(gpu_id)}
            try:
                all_results[task['method']] = task['func'](env_vars)
            except Exception:
                logger.error(f"Benchmark for {task['method']} failed.", exc_info=True)

    if all_results:
        setup_logging("Evaluator", log_file_mode='a')
        evaluate_and_summarize(all_results, args.gpu_ids[0])
    else:
        main_logger.warning("No benchmark results were successfully generated.")

if __name__ == "__main__":
    main()